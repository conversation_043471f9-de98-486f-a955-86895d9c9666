import { useEffect, useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import ContentHeader from '../../../components/partials/ContentHeader';
import SearchArticleForm from '../components/SearchArticleForm';
import ArticleRoyaltiesTabFilter from '../components/ArticleRoyaltiesTabFilter';
import ArticleList from '../components/ArticleList';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { SearchWorkflow, WorkflowQuery } from '../../../types/Workflow';
import { LIMIT_MAX, OPERATION_NAME, PAGINATION, QUERY_KEY } from '../../../constants/common';
import { WORKFLOW_LIST } from '../../../services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import { ItemStatus } from '../../../types/common/Item';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import useQueryParams from '../../../hooks/useQueryParams';
import { generateFilters } from '../../../utils/common';
import {
    articleFilterConfig,
    ArticlePageType,
    ArticleQueryRes,
    NewArticleType,
    SearchArticle,
    SearchArticleParam,
} from '../../../types/Article';
import { ARTICLE_LIST } from '../../../services/ArticleService';
import { useAuthStore } from '../../../stores/authStore';
import { USER_LIST } from '../../../services/UserService';
import { UserListQuery } from '../../../types/User';
import PaginationTable from '../../../components/partials/PaginationTable';
import { useAppStore } from '../../../stores/appStore';
import { GroupQuery, GroupType, SearchGroup } from 'types/Group';
import { GROUP_LIST } from 'services/GroupService';
import { convertDateRangeToQueryParams, getSixtyDaysAgo } from '../../../utils/date';

export default function ArticleRoyaltiesPage() {
    const currentUser = useAuthStore((state) => state.user);
    const [activeTab, setActiveTab] = useState('all');
    const [pageTitle] = useState('Quản lý nhuận bút');
    const [userIds, setUserIds] = useState<number[]>([]);
    const [selectedWorkflowIds, setSelectedWorkflowIds] = useState<string[]>([]);
    const { queryParams, setQueryParams } = useQueryParams<SearchArticleParam>();
    const departmentId = useAppStore((state) => state.departmentId);

    // Set default userIds (có thể customize logic này theo yêu cầu business)
    useEffect(() => {
        if (currentUser?.id) {
            setUserIds([currentUser.id]);
        }
    }, [currentUser]);

    const { data: workflowData } = useGraphQLQuery<WorkflowQuery, SearchWorkflow>(
        [QUERY_KEY.WORKFLOWS],
        WORKFLOW_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const workflows = useMemo(() => workflowData?.workflows_list.data ?? [], [workflowData]);

    const { data: groupData } = useGraphQLQuery<GroupQuery, SearchGroup>(
        [QUERY_KEY.GROUPS],
        GROUP_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [
                `status_id:=(${ItemStatus.ACTIVE})`,
                `department_id:=(${departmentId})`,
                `type_id:=(${GroupType.DEPARTMENT})`,
            ],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const paramConfig: SearchArticleParam = omitBy(
        {
            department_id: departmentId.toString(),
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            title: queryParams.title,
            workflow_id:
                queryParams.workflow_id ?? (selectedWorkflowIds.length > 0 ? selectedWorkflowIds.join(',') : undefined),
            article_type_id: queryParams.article_type_id === 'null' ? undefined : queryParams.article_type_id,
            created_by: queryParams.created_by,
            root_article_id: activeTab === NewArticleType.ALL ? 'null' : undefined,
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
        },
        isUndefined
    );

    // Set default date range (60 days ago to today) when component mounts
    useEffect(() => {
        if (!queryParams.created_at__range) {
            setQueryParams({
                ...queryParams,
                created_at__range: getSixtyDaysAgo(),
            });
        }
    }, []); // Only run on component mount

    useEffect(() => {
        if (queryParams.article_type_id) {
            setActiveTab(queryParams.article_type_id);
        } else {
            // Mặc định active tab "Tất cả"
            setActiveTab(NewArticleType.ALL);
        }
    }, [queryParams.article_type_id]);

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, articleFilterConfig);

    const { data, isLoading, refetch } = useGraphQLQuery<ArticleQueryRes, SearchArticle>(
        [QUERY_KEY.ARTICLES, paramConfig, filters],
        ARTICLE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: Boolean(userIds.length > 0),
        }
    );

    const handleTabChange = (tabId: string) => {
        setActiveTab(tabId);

        const params: Record<string, string> = {};

        switch (tabId) {
            case NewArticleType.ALL:
                params.article_type_id = '';
                break;
            default:
                if (!isNaN(Number(tabId))) {
                    params.article_type_id = tabId;
                }
                break;
        }

        const updatedParams: Partial<SearchArticleParam> = {
            ...params,
        };

        setQueryParams(updatedParams);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const { data: userData } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS],
        USER_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    return (
        <>
            <Helmet>
                <title>{pageTitle}</title>
            </Helmet>
            <ContentHeader title={pageTitle} />
            <div className="content-body">
                <div className="col-12">
                    <SearchArticleForm
                        isLoading={isLoading}
                        workflows={workflows}
                        articleTypeId={activeTab === NewArticleType.ALL ? '' : activeTab}
                        showDepartment={false}
                        departments={groupData?.groups_list.data ?? []}
                        isShowUser={true}
                        users={userData?.users_list.data ?? []}
                    />

                    <div className="mt-2">
                        <ArticleRoyaltiesTabFilter activeTab={activeTab} onTabChange={handleTabChange} />
                    </div>

                    <div className="mt-2">
                        {data && (
                            <div className="card">
                                <ArticleList
                                    activeTab={activeTab}
                                    displayOptions={{}}
                                    isLoading={isLoading}
                                    data={data}
                                    limit={Number(paramConfig.limit)}
                                    refetch={refetch}
                                    type={ArticlePageType.PERSONAL}
                                    articleTypeId={Number(activeTab)}
                                    isRoyaltiesPage={true}
                                />
                                <PaginationTable
                                    countItem={data.articles_list.totalCount}
                                    totalPage={data.articles_list.totalPages}
                                    currentPage={data.articles_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}
