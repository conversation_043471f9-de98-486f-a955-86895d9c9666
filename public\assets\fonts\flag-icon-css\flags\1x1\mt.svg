<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3784">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <g id="flag" fill-rule="evenodd" transform="matrix(.68808 0 0 1.0321 0 -.0000064679)" stroke-width="1pt">
  <rect id="rect706" height="496.06" width="372.05" y=".000033" x="372.05" fill="#ce0000"/>
  <rect id="rect708" height="496.06" width="372.05" y="0.000018" x="0" fill="#fff"/>
 </g>
 <g id="g647" transform="matrix(.35005 0 0 .35003 40.728 -25.527)">
  <path id="path606" d="m209.06 63.779c0 7.8276-7.932 14.173-17.717 14.173-9.7846 0-17.717-6.3456-17.717-14.173 0-7.8276 7.932-14.173 17.717-14.173 9.7846 0 17.717 6.3456 17.717 14.173z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 114.05 362.81)" fill="#f1eeee"/>
  <path id="path552" d="m171.26 485.43v17.717h17.717c0 2.952 2.953 5.905 5.906 5.905v17.716h17.716v-17.716c2.953 0 5.906-2.953 5.906-5.905h17.717v-17.717h-17.717c0-2.952-2.953-5.905-5.906-5.905v-17.716h-17.716v17.716c-2.953 0-5.906 2.953-5.906 5.905h-17.717z" fill-rule="evenodd" transform="matrix(6 0 0 6.0001 -1027.5 -2551.2)" stroke-width="1pt" fill="#c00"/>
  <path id="path553" d="m171.85 486.02v16.535h17.717c0 2.952 2.952 5.906 5.905 5.906v17.716h16.536v-17.716c2.953 0 5.905-2.954 5.905-5.906h17.717v-16.535h-17.717c0-2.952-2.952-5.906-5.905-5.906v-17.716h-16.536v17.716c-2.953 0-5.905 2.954-5.905 5.906h-17.717z" fill-rule="evenodd" transform="matrix(6 0 0 6.0001 -1027.5 -2551.2)" stroke-width="1pt" fill="#ffe600"/>
  <path id="path554" d="m172.44 486.61v15.355h17.716c0 2.952 2.953 5.905 5.906 5.905v17.716h15.354v-17.716c2.953 0 5.906-2.953 5.906-5.905h17.716v-15.355h-17.716c0-2.952-2.953-5.905-5.906-5.905v-17.716h-15.354v17.716c-2.953 0-5.906 2.953-5.906 5.905h-17.716z" fill-rule="evenodd" transform="matrix(6 0 0 6.0001 -1027.6 -2551.2)" stroke-width="1pt" fill="#707070"/>
  <path id="path555" d="m173.03 487.2v14.173h17.716c0 2.952 2.953 5.906 5.906 5.906v17.716h14.173v-17.716c2.953 0 5.906-2.954 5.906-5.906h17.716v-14.173h-17.716c0-2.952-2.953-5.905-5.906-5.905v-17.716h-14.173v17.716c-2.953 0-5.906 2.953-5.906 5.905h-17.716z" fill-rule="evenodd" transform="matrix(6 0 0 6.0001 -1027.6 -2551.2)" stroke-width="1pt" fill="#a0a0a0"/>
  <g id="g562" fill-rule="evenodd" transform="matrix(.70711 -.70711 .70711 .70711 -304.01 361.65)">
   <path id="path556" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(1.2857 0 0 1 -80.99 3.5432)" fill="#d0d0d0"/>
   <path id="path557" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(.85715 0 0 1 51.124 3.5432)" fill="#808080"/>
   <path id="path560" d="m281.34 223.23h4.96l4.961 14.174 4.96-14.174h4.961l-9.921 28.347-9.921-28.347z" transform="matrix(.71429 0 0 .74998 100.22 123.13)" fill="#d0d0d0"/>
   <rect id="rect561" height="21.26" width="3.5433" y="290.55" x="322.44" stroke-width="1pt" fill="#d0d0d0"/>
  </g>
  <g id="g567" fill-rule="evenodd" transform="matrix(.70711 .70711 -.70711 .70711 247.8 -84.327)">
   <path id="path568" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(1.2857 0 0 1 -80.99 3.5432)" fill="#d0d0d0"/>
   <path id="path569" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(.85715 0 0 1 51.124 3.5432)" fill="#808080"/>
   <path id="path570" d="m281.34 223.23h4.96l4.961 14.174 4.96-14.174h4.961l-9.921 28.347-9.921-28.347z" transform="matrix(.71429 0 0 .74998 100.22 123.13)" fill="#d0d0d0"/>
   <rect id="rect571" height="21.26" width="3.5433" y="290.55" x="322.44" stroke-width="1pt" fill="#d0d0d0"/>
  </g>
  <g id="g572" fill-rule="evenodd" transform="matrix(-.70711 .70711 -.70711 -.70711 693.78 467.48)">
   <path id="path573" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(1.2857 0 0 1 -80.99 3.5432)" fill="#d0d0d0"/>
   <path id="path574" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(.85715 0 0 1 51.124 3.5432)" fill="#808080"/>
   <path id="path575" d="m281.34 223.23h4.96l4.961 14.174 4.96-14.174h4.961l-9.921 28.347-9.921-28.347z" transform="matrix(.71429 0 0 .74998 100.22 123.13)" fill="#d0d0d0"/>
   <rect id="rect576" height="21.26" width="3.5433" y="290.55" x="322.44" stroke-width="1pt" fill="#d0d0d0"/>
  </g>
  <g id="g577" fill-rule="evenodd" transform="matrix(-.70711 -.70711 .70711 -.70711 141.97 913.46)">
   <path id="path578" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(1.2857 0 0 1 -80.99 3.5432)" fill="#d0d0d0"/>
   <path id="path579" d="m333.07 295.87c0 8.8061-11.105 15.945-24.803 15.945s-24.803-7.1388-24.803-15.945c0-8.8061 11.105-15.945 24.803-15.945s24.803 7.1388 24.803 15.945z" transform="matrix(.85715 0 0 1 51.124 3.5432)" fill="#808080"/>
   <path id="path580" d="m281.34 223.23h4.96l4.961 14.174 4.96-14.174h4.961l-9.921 28.347-9.921-28.347z" transform="matrix(.71429 0 0 .74998 100.22 123.13)" fill="#d0d0d0"/>
   <rect id="rect581" height="21.26" width="3.5433" y="290.55" x="322.44" stroke-width="1pt" fill="#d0d0d0"/>
  </g>
  <path id="path582" d="m269.29 414.57c0 41.095-34.107 74.409-76.181 74.409s-76.181-33.314-76.181-74.409 34.107-74.409 76.181-74.409 76.181 33.314 76.181 74.409z" transform="matrix(.97674 0 0 1 6.2626 .00019836)" stroke="#707070" stroke-width="1pt" fill="none"/>
  <path id="path583" d="m269.29 414.57c0 41.095-34.107 74.409-76.181 74.409s-76.181-33.314-76.181-74.409 34.107-74.409 76.181-74.409 76.181 33.314 76.181 74.409z" transform="matrix(.74419 0 0 .76190 51.172 98.707)" stroke="#707070" stroke-width="1pt" fill="none"/>
  <path id="path584" d="m170.08 474.8c0 1.9569-1.5864 3.5433-3.5433 3.5433s-3.5433-1.5864-3.5433-3.5433 1.5864-3.5433 3.5433-3.5433 3.5433 1.5864 3.5433 3.5433z" fill-rule="evenodd" transform="translate(0 -.000030518)" fill="#d0d0d0"/>
  <path id="path585" d="m170.08 474.8c0 1.9569-1.5864 3.5433-3.5433 3.5433s-3.5433-1.5864-3.5433-3.5433 1.5864-3.5433 3.5433-3.5433 3.5433 1.5864 3.5433 3.5433z" fill-rule="evenodd" transform="translate(56.693 -.000030518)" fill="#d0d0d0"/>
  <path id="path586" d="m318.9 315.35v7.087h-7.087c-7.087 0-7.087 7.087 0 7.087h7.087v7.086c0.024 7.347 6.185 8.232 6.636 0.573l0.45-7.659h7.087c7.086 0 7.086-7.087 0-7.087h-7.087v-7.087c0-7.086-7.086-7.086-7.086 0z" fill-rule="evenodd" transform="matrix(.66667 0 0 .52133 -20.079 310.4)" fill="#d0d0d0"/>
  <path id="path590" d="m180.71 162.99v7.087s-3.362 5.423-24.803 7.086-24.803 0-24.803 0c0.559 6.181 4.732 17.896 3.543 31.89 0.835 16.291-4.103 31.042-4.103 31.042 0.56 0.848-2.018 1.279-6.527 0.848 3.543-3.544 3.134-13.795 3.543-28.347 0.409-14.553-13.527-36.456-10.63-42.519s63.78-7.087 63.78-7.087z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 117.56 374.28)" stroke="#707070" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path591" d="m272.84 162.26c20.858-1.077 55.859-0.843 55.859-0.843s8.253 18.502 7.91 28.723-3.181 18.919-7.077 14.57 1.805-3.531 2.498-14.57c0.692-11.039-6.661-22.479-6.661-22.479s-14.962 7.302-37.466 5.828c-22.503-1.474-12.541-10.791-15.063-11.229z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 115.22 366.9)" stroke="#707070" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path588" d="m312.21 273.91c20.858-1.077 34.551 2.498 34.551 2.498s8.253 18.502 7.91 28.723-3.181 18.919-7.077 14.57 1.805-3.531 2.498-14.57c0.692-11.039-6.661-22.479-6.661-22.479s-14.962 7.302-37.466 5.828c-22.503-1.474-16.084-2.975-18.606-3.413s-3.131 6.958-37.176 7.992c-34.044 1.033-51.407-7.992-51.407-7.992s-3.362 5.423-24.803 7.086-24.803 0-24.803 0c0.559 6.181 4.732 17.896 3.543 31.89 0.835 16.291-4.103 31.042-4.103 31.042 0.56 0.848-2.018 1.279-6.527 0.848 3.543-3.544 3.134-13.795 3.543-28.347 0.409-14.553-13.527-36.456-10.63-42.519s18.333-0.395 21.26-3.544c2.926-3.149-13.749-25.333-10.63-46.063 3.12-20.731 20.792-14.886 42.52-14.173 21.727 0.712 69.865 7.003 81.496 0 11.632-7.003 6.598-32.929 17.717-49.606 7.086-10.63 17.716-14.173 28.346-14.173 7.087 0 17.717 28.346 17.717 35.433l-3.544 3.543h-3.543v3.543c-8.088 0.907-9.216-7.732-14.173-14.173-2.13 9.984 7.086 42.52 7.086 63.78 0 17.716-6.622 25.525-7.038 24.276z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 104.56 339.3)" stroke="#707070" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path592" d="m209.06 131.1c0.294 7.062-27.384 22.895-29.637 29.164s7.036 9.744 4.163 13.321c-2.872 3.578-5.24 2.251-5.828 2.914-0.588 0.664-10.407-24.56-10.407-24.56s25.961-13.084 24.977-18.733-19.171-9.085-22.479-23.312c-3.309-14.227-1.474-41.71-4.163-47.456-2.689-5.7468-34.552-11.23-34.579-19.92-0.027-8.6904 32.75-32.541 36.293-36.085 3.543-3.5432 7.087 3.5433 3.543 7.0866-3.543 3.5433-29.206 25.455-29.206 28.998 0 3.5433 21.26 7.0866 28.347 7.0866 7.086 0 10.63-3.5433 10.63-7.0866 0.042-3.0655-7.087-3.5433-7.087-10.63 0-7.0866 3.543-10.63 10.63-10.63 3.543 0 10.356 4.5372 10.63 10.63 0.274 6.0927-6.305 6.6088-7.087 10.63-0.781 4.0212 7.087 7.0866 14.173 7.0866 7.087 0 31.282 20.608 34.825 24.152 3.544 3.5434 7.087 3.5433 3.544 7.0867-3.543 3.5433-1.468 7.7381-5.012 4.1948-3.017-3.423-3.543-7.0867-3.543-7.0867-0.306-3.2197-26.27-17.716-29.814-14.173-3.543 3.5433 0 46.064 0 49.606 0 7.086 7.381 7.062 7.087 17.716z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 116.1 372.43)" stroke="#707070" stroke-width="1pt" fill="#d0d0d0"/>
  <rect id="rect604" fill-rule="evenodd" height="14.662" width="14.045" y="386.9" x="170.23" stroke-width="1pt" fill="#a7a7a7"/>
  <rect id="rect593" transform="matrix(.55440 .83225 -.88236 .47057 0 0)" fill-rule="evenodd" rx="1.2699" ry=".56972" height="1.1394" width="87.553" y="64.582" x="405.61" stroke-width="1pt" fill="#808080"/>
  <path id="path594" d="m180.71 42.52c0 3.9138-3.1728 7.0866-7.0866 7.0866s-7.0866-3.1728-7.0866-7.0866 3.1728-7.0866 7.0866-7.0866 7.0866 3.1728 7.0866 7.0866z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 114.05 362.81)" stroke="#707070" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path596" d="m354.33 49.606c0 4.8899-27.965 10.63-60.237 10.63s-60.236-5.74-60.236-10.63c0-4.8897 27.964 7.0866 60.236 7.0866s60.237-11.976 60.237-7.0866z" fill-rule="evenodd" transform="matrix(.19431 0 0 .17745 145.85 385.64)" stroke-width="1pt" fill="#808080"/>
  <rect id="rect603" fill-rule="evenodd" height="4.189" width="11.704" y="401.56" x="171.4" stroke-width="1pt" fill="#a7a7a7"/>
  <path id="path597" d="m279.92 58.465c0 6.8492-0.7932 12.402-1.7717 12.402-0.97847 0-1.7717-5.5524-1.7717-12.402 0-6.8492 0.7932-12.402 1.7717-12.402 0.97846 0 1.7717 5.5524 1.7717 12.402z" fill-rule="evenodd" transform="matrix(.19766 .26014 -.16568 .31035 163.81 300.28)" fill="#808080"/>
  <path id="path598" d="m279.92 58.465c0 6.8492-0.7932 12.402-1.7717 12.402-0.97847 0-1.7717-5.5524-1.7717-12.402 0-6.8492 0.7932-12.402 1.7717-12.402 0.97846 0 1.7717 5.5524 1.7717 12.402z" fill-rule="evenodd" transform="matrix(.037095 .23343 -.18868 .16554 213.08 321.72)" fill="#808080"/>
  <path id="path599" d="m279.92 58.465c0 6.8492-0.7932 12.402-1.7717 12.402-0.97847 0-1.7717-5.5524-1.7717-12.402 0-6.8492 0.7932-12.402 1.7717-12.402 0.97846 0 1.7717 5.5524 1.7717 12.402z" fill-rule="evenodd" transform="matrix(.18217 -.077726 .13913 .25309 151.8 401.05)" fill="#808080"/>
  <path id="path600" d="m290.55 136.42c0 0.97846-10.312 1.7717-23.032 1.7717s-23.031-0.7932-23.031-1.7717c0-0.97846 10.312-1.7717 23.031-1.7717 12.72 0 23.032 0.7932 23.032 1.7717z" fill-rule="evenodd" transform="matrix(.35573 0 0 .29556 106.66 362.81)" fill="#808080"/>
  <path id="path601" d="m209.06 132.87c0 0.97846-7.932 1.7717-17.717 1.7717-9.7846 0-17.717-0.7932-17.717-1.7717 0-0.97846 7.932-1.7717 17.717-1.7717 9.7846 0 17.717 0.7932 17.717 1.7717z" fill-rule="evenodd" transform="matrix(.33032 0 0 .29556 114.05 362.81)" fill="#808080"/>
  <path id="path609" d="m375.49 171.92s8.938 36.133 47.04 36.633 51.215-10.838 35.8-29.972-44.084-13.182-40.379 3.33-10.752 22.019-14.154 10.407c-3.401-11.611-17.045-45.99 7.493-48.289 24.539-2.299 76.511-14.817 89.501 9.159 12.991 23.975-14.675 45.707 6.244 50.786 20.92 5.079 23.893-5.809 59.896-9.094 36.004-3.286 49.742 11.102 62.076 9.094 12.334-2.007 10.59-37.258 0.832-46.207-9.758-8.95-28.5-10.586-33.303-20.398-4.802-9.812-17.87-1.825-16.651-12.905s18.53 7.625 18.733 1.966c0.006-0.177 2.858-3.93 3.434-4.878-2.178-0.828-3.421 0.512-5.056 1.948 1.775-2.36 1.665-3.448 2.26-5.378-0.31-0.727-2.461 2.734-2.825 2.011s2.505-5.632 1.206-3.852c-0.45 0.513-3.872 3.329-3.91 1.389 0.058-0.787 1.789-2.813 1.177-3.912-0.493-0.914-3.093 1.064-3.428 1.29 1.487-3.309 1.382-3.269-0.048-5.721-1.249-2.452-2.16-2.395-4.2 0.374 0.849-3.979 1.08-4.821-0.969-6.329-1.685-1.521-1.76-2.956-3.856 4.162 0.798-7.618-1.343-7.9501-0.454-11.562 0.529-3.3116 3.323-4.7092 7.927-3.9776 8.945 0.1301 13.728 17.954 16.651 17.9 2.924-0.054 6.196-18.882 18.733-11.24 12.537 7.6426 14.085 9.2016 20.398 13.738 6.313 4.535-4.435 11.457-0.833 20.398 3.602 8.94 28.229 38.804 21.231 69.935-6.999 31.131-25.026 36.055-26.642 49.538-1.617 13.483 57.067 7.951 72.017 12.489 14.949 4.537 24.375 23.11 24.977 26.225s-26.226 2.914-26.226 2.914-13.776-11.837-46.624-12.904c-32.847-1.068-45.355-23.109-57.651-25.871s-38.501 4.63-53.149 3.543c-14.649-1.087-29.488-4.943-38.976-7.086-9.489-2.142-56.693 35.433-56.529 44.184-9.84-0.621-33.009-2.709-32.054-1.665 0.955 1.043 4.152-17.038 22.214-20.216 18.061-3.179 35.645-17.573 35.062-27.025-0.583-9.451 3.487-41.876 4.995-55.782s8.27-20.616 6.661-27.891c-1.609-7.274-22.244-14.778-42.045-14.57-19.801 0.209-41.77 2.057-44.959 9.991-3.188 7.934-0.781 30.964 3.331 28.308 4.111-2.657-0.994-17.712 3.33-20.815 4.324-3.102 15.405-5.843 25.809-6.244 10.405-0.401 28.494 14.729 28.308 19.565-0.186 4.837 5.821 20.934-2.498 27.059-8.318 6.124-25.332 8.862-41.628 7.493-16.297-1.369-32.786-10.674-38.715-16.651-5.928-5.977-9.574-24.145-9.574-25.394z" fill-rule="evenodd" transform="matrix(.21598 0 0 .12989 76.265 416.17)" stroke="#808080" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path610" d="m205.51 434.06c0 0.97847-0.7932 1.7717-1.7716 1.7717-0.97846 0-1.7716-0.7932-1.7716-1.7717 0-0.97846 0.79319-1.7717 1.7716-1.7717 0.97845 0 1.7716 0.7932 1.7716 1.7717z" fill-rule="evenodd" transform="matrix(.53006 0 0 .53007 100.54 201.47)"/>
  <path id="path615" d="m187.24 167.99v-3.639l-3.31-4.951h1.383l1.693 2.59c0.312 0.484 0.603 0.969 0.873 1.453 0.258-0.449 0.57-0.955 0.938-1.517l1.664-2.526h1.324l-3.428 4.951v3.639h-1.137z" transform="matrix(-.42644 1.9779 -1.2525 -.27003 543.87 92.986)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path618" d="m171.01 167.99v-7.576h-2.83v-1.014h6.808v1.014h-2.841v7.576h-1.137z" transform="matrix(.84644 1.8378 -1.1637 0.536 296.6 -19.872)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path619" d="m160.14 167.99v-8.59h1.166l4.511 6.744v-6.744h1.09v8.59h-1.166l-4.512-6.75v6.75h-1.089z" transform="matrix(1.3664 1.4922 -.94493 .86528 172.12 -16.482)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path622" d="m145.43 167.99v-8.59h1.136v7.576h4.231v1.014h-5.367z" transform="matrix(2.0085 .24467 -.15493 1.2718 -69.341 104.28)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path623" d="m138.76 167.99v-8.59h1.137v7.576h4.23v1.014h-5.367z" transform="matrix(1.9907 -.36212 .22931 1.2606 -135.47 193.57)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path626" d="m125.48 164.62v-1.008l3.639-0.006v3.188c-0.559 0.445-1.135 0.781-1.728 1.008-0.594 0.222-1.204 0.334-1.829 0.334-0.843 0-1.611-0.18-2.302-0.539-0.688-0.364-1.207-0.887-1.559-1.571-0.351-0.683-0.527-1.447-0.527-2.291 0-0.836 0.174-1.615 0.521-2.338 0.352-0.726 0.856-1.265 1.512-1.617 0.656-0.351 1.412-0.527 2.268-0.527 0.621 0 1.181 0.101 1.681 0.304 0.504 0.2 0.899 0.479 1.184 0.838 0.285 0.36 0.502 0.828 0.65 1.407l-1.025 0.281c-0.129-0.438-0.289-0.781-0.481-1.031-0.191-0.25-0.465-0.45-0.82-0.598-0.355-0.152-0.75-0.229-1.184-0.229-0.519 0-0.968 0.081-1.347 0.241-0.379 0.156-0.686 0.363-0.92 0.621-0.231 0.258-0.41 0.541-0.539 0.849-0.219 0.532-0.328 1.108-0.328 1.729 0 0.766 0.13 1.406 0.392 1.922 0.266 0.515 0.651 0.898 1.154 1.148 0.504 0.25 1.04 0.375 1.606 0.375 0.492 0 0.973-0.094 1.441-0.281 0.469-0.191 0.825-0.395 1.067-0.609v-1.6h-2.526z" transform="matrix(1.5794 -1.2646 .80082 1.0001 -182.99 365.04)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path631" d="m92.859 167.99v-8.59h5.7949v1.014h-4.6582v2.66h4.0313v1.014h-4.0313v3.902h-1.1367z" transform="matrix(-1.1715 -1.6497 1.0447 -.74182 79.96 727.93)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path634" d="m112.11 187.63c0 6.42 0.658 11.488 1.978 15.191 1.328 3.685 2.99 5.527 4.987 5.527 2.032 0 3.703-1.867 5.013-5.595 1.319-3.734 1.978-9.025 1.978-15.886 0-4.336-0.283-8.12-0.849-11.339-0.558-3.25-1.379-5.756-2.465-7.524-1.079-1.793-2.289-2.692-3.637-2.692-1.911 0-3.561 1.724-4.944 5.161-1.374 3.417-2.061 9.137-2.061 17.157zm-2.785-0.105c0-8.846 0.909-15.768 2.73-20.756 1.819-5.018 4.17-7.524 7.046-7.524 1.885 0 3.585 1.172 5.099 3.523s2.664 5.633 3.454 9.851c0.798 4.193 1.198 8.957 1.198 14.285 0 5.403-0.419 10.235-1.253 14.503-0.837 4.262-2.021 7.5-3.551 9.702-1.534 2.183-3.186 3.275-4.959 3.275-1.923 0-3.642-1.216-5.153-3.635-1.515-2.425-2.663-5.732-3.442-9.925-0.78-4.193-1.169-8.628-1.169-13.299z" transform="matrix(-.23122 -.81918 .19878 -.056108 122.07 538.17)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path637" d="m132.37 213.47v-53.284h9.054c1.821 0 3.204 0.484 4.15 1.458 0.949 0.942 1.705 2.623 2.27 5.049 0.566 2.425 0.851 5.099 0.851 8.033 0 3.777-0.47 6.966-1.407 9.559-0.939 2.593-2.387 4.243-4.345 4.944 0.713 0.893 1.257 1.78 1.628 2.654 0.789 1.886 1.538 4.25 2.244 7.084l3.551 14.503h-3.399l-2.703-11.085c-0.789-3.201-1.438-5.645-1.949-7.344-0.511-1.694-0.97-2.878-1.378-3.561-0.4-0.676-0.809-1.147-1.227-1.414-0.307-0.174-0.808-0.254-1.502-0.254h-3.136v23.658h-2.702zm2.702-29.768h5.81c1.234 0 2.199-0.323 2.895-0.98 0.697-0.677 1.227-1.744 1.588-3.201 0.364-1.477 0.544-3.077 0.544-4.795 0-2.519-0.354-4.59-1.06-6.216-0.696-1.625-1.799-2.437-3.313-2.437h-6.464v17.629z" transform="matrix(.019205 -.85097 .20650 .0046602 88.366 527.4)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path640" d="m180.8 213.47 7.842-53.284h2.912l8.357 53.284h-3.078l-2.382-16.134h-8.538l-2.242 16.134h-2.871zm5.892-21.878h6.922l-2.129-14.757c-0.652-4.485-1.134-8.17-1.45-11.048-0.259 3.412-0.626 6.805-1.101 10.173l-2.242 15.632z" transform="matrix(.73036 -.43714 .10608 .17723 4.7742 406.1)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path643" d="m231.56 213.47 7.842-53.284h2.909l8.358 53.284h-3.078l-2.382-16.134h-8.538l-2.242 16.134h-2.869zm5.893-21.878h6.922l-2.132-14.757c-0.649-4.485-1.132-8.17-1.448-11.048-0.262 3.412-0.628 6.805-1.101 10.173l-2.241 15.632z" transform="matrix(.68796 .50123 -.12163 .16694 81.266 203.3)" stroke-width="1pt" fill="#d0d0d0"/>
  <path id="path646" d="m290.89 213.47v-53.284h9.054c1.819 0 3.202 0.484 4.151 1.458 0.946 0.942 1.702 2.623 2.27 5.049 0.566 2.425 0.849 5.099 0.849 8.033 0 3.777-0.469 6.966-1.408 9.559-0.936 2.593-2.386 4.243-4.345 4.944 0.715 0.893 1.26 1.78 1.631 2.654 0.789 1.886 1.535 4.25 2.241 7.084l3.552 14.503h-3.397l-2.703-11.085c-0.789-3.201-1.44-5.645-1.949-7.344-0.511-1.694-0.972-2.878-1.381-3.561-0.397-0.676-0.806-1.147-1.224-1.414-0.307-0.174-0.809-0.254-1.505-0.254h-3.133v23.658h-2.703zm2.703-29.768h5.807c1.236 0 2.201-0.323 2.898-0.98 0.696-0.677 1.224-1.744 1.588-3.201 0.361-1.477 0.542-3.077 0.542-4.795 0-2.519-0.352-4.59-1.058-6.216-0.697-1.625-1.802-2.437-3.316-2.437h-6.461v17.629z" transform="matrix(.19808 .82782 -.20088 .048068 236.01 144.21)" stroke-width="1pt" fill="#d0d0d0"/>
 </g>
</svg>
