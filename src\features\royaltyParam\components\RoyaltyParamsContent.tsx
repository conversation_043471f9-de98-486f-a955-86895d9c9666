import { yupResolver } from '@hookform/resolvers/yup';
import InputSwitch from 'components/partials/InputSwitch';
import UpdateButton from 'components/partials/UpdateButton';
import { isEmpty } from 'lodash';
import { useEffect } from 'react';
import { Plus, Trash2 } from 'react-feather';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ItemStatus } from 'types/common/Item';
import { RoyaltyParams, RoyaltyParamsForm } from 'types/RoyaltyParams';
import cn from 'utils/cn';
import * as yup from 'yup';

type Props = {
    onSubmit: (values: RoyaltyParamsForm) => void;
    selectedRoyaltyParam?: RoyaltyParams;
    isLoading?: boolean;
    isOpen: boolean;
};

const schema = yup
    .object({
        name: yup.string().required('error.required').trim(),
        display_order: yup.string().trim().required('error.required').typeError('error.number'),
        desc: yup.string().optional(),
        status_id: yup.mixed().oneOf(Object.values(ItemStatus)),
        values: yup
            .array()
            .of(
                yup.object({
                    value: yup.string().trim(),
                    display_order: yup.string().trim(),
                    weightedScore: yup.string().trim(),
                    name: yup.string().trim(),
                })
            )
            .test('require-all-fields', 'Tất cả các trường là bắt buộc', (value) => {
                if (!value || value.length === 0) return true;

                return value.every(
                    (item) => !!item?.value && !!item?.display_order && !!item.weightedScore && !!item.name
                );
            })
            .test('unique-display-order', 'Trường thứ tự không được trùng lặp.', function (values) {
                if (!values || values.length === 0) return true;

                const displayOrders = values.map((item) => item.display_order);
                const uniqueDisplayOrders = new Set(displayOrders);

                // Kiểm tra xem có display_order trùng lặp không

                return !(displayOrders.length !== uniqueDisplayOrders.size);
            }),
    })
    .required();

const RoyaltyParamsContent = ({ onSubmit, selectedRoyaltyParam, isLoading, isOpen }: Props) => {
    const { t } = useTranslation();

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<RoyaltyParamsForm>({
        resolver: yupResolver(schema),
        defaultValues: {
            display_order: '',
            name: '',
            desc: '',
            values: [
                {
                    display_order: '',
                    name: '',
                    value: '',
                    weightedScore: '',
                },
            ],
            status_id: ItemStatus.PENDING,
        },
    });

    const { fields, append, remove } = useFieldArray({
        name: 'values',
        control,
    });

    const addParams = () => {
        append({
            display_order: '',
            name: '',
            value: '',
            weightedScore: '',
        });
    };

    useEffect(() => {
        if (isEmpty(selectedRoyaltyParam)) return;

        reset({
            display_order: selectedRoyaltyParam.display_order.toString(),
            name: selectedRoyaltyParam.name,
            desc: selectedRoyaltyParam.desc,
            values: selectedRoyaltyParam.values,
            status_id: selectedRoyaltyParam.status_id,
        });
    }, [selectedRoyaltyParam, reset]);

    useEffect(
        () => () =>
            reset({
                display_order: '',
                name: '',
                desc: '',
                values: [
                    {
                        display_order: '',
                        name: '',
                        value: '',
                        weightedScore: '',
                    },
                ],
                status_id: ItemStatus.PENDING,
            }),
        [isOpen, reset]
    );

    return (
        <div className="card-body">
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="row">
                    <div className="col-12 mb-1">
                        <label className="form-label">
                            Thứ tự hiển thị <span className="error">*</span>
                        </label>
                        <Controller
                            name="display_order"
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                                <>
                                    <input
                                        {...field}
                                        className={cn('form-control', {
                                            'is-invalid': error?.message,
                                        })}
                                        type="number"
                                    />
                                    <span className="error">{t(error?.message || '')}</span>
                                </>
                            )}
                        />
                    </div>
                    <div className="col-12 mb-1">
                        <label className="form-label">
                            Tên tham số <span className="error">*</span>
                        </label>
                        <Controller
                            name="name"
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                                <>
                                    <input
                                        {...field}
                                        type="text"
                                        className={cn('form-control', {
                                            'is-invalid': error?.message,
                                        })}
                                    />
                                    <span className="error">{t(error?.message || '')}</span>
                                </>
                            )}
                        />
                    </div>
                    <div className="col-12 mb-1">
                        <label className="form-label">Mô tả</label>
                        <Controller
                            name="desc"
                            control={control}
                            render={({ field }) => (
                                <>
                                    <input {...field} type="text" className="form-control" />
                                </>
                            )}
                        />
                    </div>
                    <div className="table-responsive mb-1">
                        <table
                            className="table mb-0"
                            style={{
                                tableLayout: 'fixed',
                            }}
                        >
                            <thead>
                                <tr>
                                    <th
                                        className="text-center w-[60px] !px-[4px]"
                                        style={{
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        Thứ tự
                                    </th>
                                    <th
                                        style={{
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        Tên
                                    </th>
                                    <th
                                        className="text-center !px-[4px] w-[60px]"
                                        style={{
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        Giá trị
                                    </th>
                                    <th
                                        className="text-center !px-[4px] w-[80px]"
                                        style={{
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        Trọng số
                                    </th>
                                    <th
                                        className="!px-[4px] w-[40px]"
                                        style={{
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        <button
                                            type="button"
                                            title="Thêm"
                                            className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                            onClick={addParams}
                                        >
                                            <Plus size={14} />
                                        </button>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {fields.map((item, index) => (
                                    <tr key={item.id}>
                                        <td className="text-center !px-[4px]">
                                            <Controller
                                                name={`values.${index}.display_order`}
                                                control={control}
                                                render={({ field }) => (
                                                    <input {...field} className="form-control" type="number" />
                                                )}
                                            />
                                        </td>
                                        <td className="!px-[4px]">
                                            <Controller
                                                name={`values.${index}.name`}
                                                control={control}
                                                render={({ field }) => (
                                                    <input {...field} className="form-control" type="text" />
                                                )}
                                            />
                                        </td>
                                        <td className="text-center !px-[4px]">
                                            <Controller
                                                name={`values.${index}.value`}
                                                control={control}
                                                render={({ field }) => (
                                                    <input {...field} className="form-control" type="number" />
                                                )}
                                            />
                                        </td>
                                        <td className="text-center !px-[4px]">
                                            <Controller
                                                name={`values.${index}.weightedScore`}
                                                control={control}
                                                render={({ field }) => (
                                                    <input {...field} className="form-control" type="number" />
                                                )}
                                            />
                                        </td>
                                        <td className="text-center !px-[4px]">
                                            <button
                                                type="button"
                                                title="Xoá"
                                                className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                onClick={() => remove(index)}
                                            >
                                                <Trash2 size={14} />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                        <span className="error">{t(errors?.values?.message || '')}</span>
                    </div>
                    <div className="col-12 mb-1">
                        <Controller
                            control={control}
                            name="status_id"
                            render={({ field }) => (
                                <InputSwitch
                                    name="status_id"
                                    labelFieldName="Trạng thái"
                                    labelSwitchName="Hoạt động"
                                    checked={field.value === ItemStatus.ACTIVE}
                                    onChange={(e) =>
                                        field.onChange(e.target.checked ? ItemStatus.ACTIVE : ItemStatus.PENDING)
                                    }
                                />
                            )}
                        />
                    </div>

                    <div>
                        <UpdateButton btnText="Cập nhật" isLoading={isLoading} />
                    </div>
                </div>
            </form>
        </div>
    );
};

export default RoyaltyParamsContent;
