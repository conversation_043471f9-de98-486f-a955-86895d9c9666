import Article from './Article';
import { BaseModel, DataList } from './common';

export default interface ArticleLog extends BaseModel {
    article_id: number;
    content: string;
    article: Article;
}

export interface ArticleLogsQueryRes {
    article_logs_list: DataList<ArticleLog>;
}

export enum ArticleLogAction {
    ARTICLE_UPDATED = 'ARTICLE_UPDATED',
    ARTICLE_CREATED = 'ARTICLE_CREATED',
}
