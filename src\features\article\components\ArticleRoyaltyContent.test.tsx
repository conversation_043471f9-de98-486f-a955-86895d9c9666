import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ArticleRoyaltyContent from './ArticleRoyaltyContent';

// Mock the stores and hooks
jest.mock('stores/appStore', () => ({
    useAppStore: () => ({ departmentId: 1 }),
}));

jest.mock('hooks/useGraphQLQuery', () => ({
    useGraphQLQuery: jest.fn(() => ({
        data: {
            article_royalties_list: {
                data: [
                    {
                        id: 1,
                        article_id: 1,
                        type_id: 1,
                        suggest_royalty: 100,
                        articleRoyaltyUsers: [
                            {
                                id: 1,
                                article_royalty_id: 1,
                                user_id: 1,
                                percent: 50,
                                comment: 'Test comment',
                                param_config: [],
                                final_royalty: 50,
                            },
                        ],
                    },
                ],
            },
            royalty_params_list: {
                data: [
                    {
                        id: 1,
                        name: 'Test Param',
                        values: [
                            { name: 'Option 1', value: '1' },
                            { name: 'Option 2', value: '2' },
                        ],
                    },
                ],
            },
            users_list: {
                data: [
                    { id: 1, full_name: 'Test User' },
                    { id: 2, full_name: 'Test User 2' },
                ],
            },
        },
    })),
}));

const queryClient = new QueryClient({
    defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
    },
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('ArticleRoyaltyContent', () => {
    it('renders table headers correctly', () => {
        render(
            <TestWrapper>
                <ArticleRoyaltyContent articleId={1} />
            </TestWrapper>
        );

        expect(screen.getByText('STT')).toBeInTheDocument();
        expect(screen.getByText('Nhóm')).toBeInTheDocument();
        expect(screen.getByText('Đề xuất')).toBeInTheDocument();
        expect(screen.getByText('Người thực hiện')).toBeInTheDocument();
        expect(screen.getByText('Điểm')).toBeInTheDocument();
        expect(screen.getByText('Ghi chú')).toBeInTheDocument();
        expect(screen.getByText('Action')).toBeInTheDocument();
    });

    it('renders add button when no users exist', () => {
        render(
            <TestWrapper>
                <ArticleRoyaltyContent articleId={1} />
            </TestWrapper>
        );

        const addButtons = screen.getAllByTitle('Thêm người thực hiện');
        expect(addButtons.length).toBeGreaterThan(0);
    });

    it('can add new user', () => {
        render(
            <TestWrapper>
                <ArticleRoyaltyContent articleId={1} />
            </TestWrapper>
        );

        const addButton = screen.getAllByTitle('Thêm người thực hiện')[0];
        fireEvent.click(addButton);

        // Should show user selection dropdown after adding
        expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
});
