let isSimulating = false;

export default function handleKeyDown() {
    const handleKeyDown = (event: KeyboardEvent) => {
        if (isSimulating || event.key !== 'Enter') return;

        const active = document.activeElement as HTMLElement | null;

        if (active?.tagName === 'INPUT' && active.closest('.react-tags')) {
            isSimulating = true;

            const arrowDown = new KeyboardEvent('keydown', {
                key: 'ArrowDown',
                bubbles: true,
            });
            active.dispatchEvent(arrowDown);

            setTimeout(() => {
                const enter = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    bubbles: true,
                });
                active.dispatchEvent(enter);
                isSimulating = false;
            }, 50);
        }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
}
