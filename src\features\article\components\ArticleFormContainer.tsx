import { useMemo } from 'react';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import UpdateArticleForm from './UpdateArticleForm';
import { useAppStore } from '../../../stores/appStore';
import { baseFilterConfig, BaseSearch, BaseSearchParam } from '../../../types/common';
import { LIMIT_MAX, OPERATION_NAME, QUERY_KEY } from '../../../constants/common';
import { ItemStatus } from '../../../types/common/Item';
import { generateFilters } from '../../../utils/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { TemplateQuery } from '../../../types/Template';
import { TEMPLATE_LIST } from '../../../services/TemplateService';
import { pseudonymFilterConfig, PseudonymQuery, SearchPseudonymParam } from '../../../types/Pseudonym';
import { PSEUDONYM_LIST } from '../../../services/PseudonymService';
import { keepPreviousData, QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { useAuthStore } from '../../../stores/authStore';
import {
    categoryFilterConfig,
    CategoryQuery,
    CategoryType,
    SearchCategory,
    SearchCategoryParam,
} from '../../../types/Category';
import { CATEGORY_LIST } from '../../../services/CategoryService';
import { layoutFilterConfig, LayoutQuery, LayoutType, SearchLayout, SearchLayoutParam } from '../../../types/Layout';
import { LAYOUT_LIST } from '../../../services/LayoutService';
import { TagQuery } from '../../../types/Tag';
import { TAG_LIST } from '../../../services/TagService';
import { WORKFLOW_PERMISSION_ARTICLE_TYPE_LIST } from '../../../services/WorkflowPermissionArticleTypeService';
import {
    workflowPermissionArticleTypeFilterConfig,
    WorkflowPermissionArticleTypeQuery,
} from '../../../types/WorkflowPermissionArticleType';
import { SearchWorkflow, SearchWorkflowParam, workflowFilterConfig, WorkflowQuery } from '../../../types/Workflow';
import {
    SearchWorkflowPermission,
    SearchWorkflowPermissionParam,
    workflowPermissionFilterConfig,
    WorkflowPermissionQuery,
} from '../../../types/WorkflowPermission';
import { WORKFLOW_PERMISSION_LIST } from '../../../services/WorkflowPermissionService';
import { WORKFLOW_LIST } from '../../../services/WorkflowService';
import Article, { ArticleDetailQueryRes, ArticlePageType } from '../../../types/Article';

export interface IProps {
    typeId: number;
    type: ArticlePageType;
    id: number | null;
    article?: Article;
    refetchArticle?: (options?: RefetchOptions) => Promise<QueryObserverResult<ArticleDetailQueryRes, Error>>;
}

export default function ArticleFormContainer({ typeId, type, id, article, refetchArticle }: Readonly<IProps>) {
    const departmentId = useAppStore((state) => state.departmentId);
    const currentUser = useAuthStore((state) => state.user);
    const userId = useMemo(() => currentUser?.id ?? 0, [currentUser]);

    const baseParamConfig: BaseSearchParam = {
        status_id: ItemStatus.ACTIVE.toString(),
        department_id: departmentId.toString(),
    };

    {
        /** get list template */
    }
    const templateParamConfig: BaseSearchParam = omitBy(
        {
            ...baseParamConfig,
            limit: LIMIT_MAX,
            page: '1',
            search: '',
        },
        isUndefined
    );

    const { search, limit, page, department_id, ...dataTemplateParamConfig } = templateParamConfig;
    const templateFilters = generateFilters(dataTemplateParamConfig, baseFilterConfig);

    const { data: templateData } = useGraphQLQuery<TemplateQuery, BaseSearch>(
        [QUERY_KEY.TEMPLATES, templateParamConfig, templateFilters],
        TEMPLATE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: templateFilters.length > 0 ? templateFilters : undefined,
        },
        '',
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );
    const templates = templateData?.templates_list.data ?? [];
    {
        /** End get list template */
    }

    {
        /** get list pseudonym */
    }
    const pseudonymParamConfig: SearchPseudonymParam = omitBy(
        {
            status_id: ItemStatus.ACTIVE,
            user_id: userId,
        },
        isUndefined
    );
    const pseudonymFilters = generateFilters(pseudonymParamConfig, pseudonymFilterConfig);
    const { data: dataListPseudonym } = useGraphQLQuery<PseudonymQuery, BaseSearch>(
        [QUERY_KEY.PSEUDONYMS, pseudonymParamConfig, pseudonymFilters],
        PSEUDONYM_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: pseudonymFilters.length > 0 ? pseudonymFilters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const pseudonyms = dataListPseudonym?.pseudonyms_list.data ?? [];
    {
        /** End get list pseudonym */
    }

    {
        /** get list category */
    }
    const baseCategoryParamConfig: SearchCategoryParam = omitBy(
        {
            ...baseParamConfig,
            article_type_id: typeId,
        },
        isUndefined
    );
    const categoryParamConfig: SearchCategoryParam = omitBy(
        {
            ...baseCategoryParamConfig,
            category_type_id: CategoryType.CATEGORY,
        },
        isUndefined
    );
    const categoryFilters = generateFilters(categoryParamConfig, categoryFilterConfig);
    const { data: categoryData } = useGraphQLQuery<CategoryQuery, SearchCategory>(
        [QUERY_KEY.CATEGORIES, categoryParamConfig, categoryFilters],
        CATEGORY_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: categoryFilters,
            sorts: ['display_order:ASC', 'children.display_order:ASC'],
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );
    const categories = categoryData?.categories_list.data ?? [];
    {
        /** End get list category */
    }

    {
        /** get list topic */
    }
    const topicParamConfig: SearchCategoryParam = omitBy(
        {
            ...baseCategoryParamConfig,
            category_type_id: CategoryType.TOPIC,
        },
        isUndefined
    );
    const topicFilters = generateFilters(topicParamConfig, categoryFilterConfig);
    const { data: topicData } = useGraphQLQuery<CategoryQuery, SearchCategory>(
        [QUERY_KEY.CATEGORIES, topicParamConfig, topicFilters],
        CATEGORY_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: topicFilters,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );
    const topics = topicData?.categories_list.data ?? [];
    {
        /** End get list topic */
    }

    {
        /** get list layout */
    }
    const layoutParamConfig: SearchLayoutParam = omitBy(
        {
            ...baseParamConfig,
            layout_type_id: LayoutType.DETAIL,
        },
        isUndefined
    );
    const layoutFilters = generateFilters(layoutParamConfig, layoutFilterConfig);
    const { data: layoutData } = useGraphQLQuery<LayoutQuery, SearchLayout>(
        [QUERY_KEY.LAYOUTS, layoutParamConfig, layoutFilters],
        LAYOUT_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: layoutFilters,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );
    const layouts = layoutData?.layouts_list.data ?? [];
    {
        /** End get list layout */
    }

    {
        /** get list tags */
    }
    const tagFilters = generateFilters(baseParamConfig, baseFilterConfig);
    const { data: tagData } = useGraphQLQuery<TagQuery, BaseSearch>(
        [QUERY_KEY.TAGS, baseParamConfig, tagFilters],
        TAG_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: tagFilters,
        },
        '',
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );
    const tags = tagData?.tags_list.data ?? [];
    {
        /** End get list tags */
    }

    const ud = currentUser?.userDepartments?.find((item) => item.department.id === departmentId);
    const { data: workflowPermissionArticleTypeData } = useGraphQLQuery<WorkflowPermissionArticleTypeQuery, BaseSearch>(
        [QUERY_KEY.WORKFLOW_PERMISSION_ARTICLE_TYPES, ud],
        WORKFLOW_PERMISSION_ARTICLE_TYPE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: generateFilters(
                { user_department_id: ud?.id.toString() },
                workflowPermissionArticleTypeFilterConfig
            ),
        },
        '',
        { enabled: Boolean(ud) }
    );
    const workflowArticleTypeId = workflowPermissionArticleTypeData?.workflow_permission_article_types_list.find(
        (item) => item.article_type_id === typeId
    );

    const paramConfigWorkflowPermission: SearchWorkflowPermissionParam = omitBy(
        {
            status_id: ItemStatus.ACTIVE.toString(),
            department_id: departmentId.toString(),
            id: workflowArticleTypeId?.workflow_permission_id,
        },
        isUndefined
    );
    const filterWorkflowPermissions = generateFilters(paramConfigWorkflowPermission, workflowPermissionFilterConfig);

    const { data: workflowPermissionData } = useGraphQLQuery<WorkflowPermissionQuery, SearchWorkflowPermission>(
        [QUERY_KEY.WORKFLOW_PERMISSIONS, paramConfigWorkflowPermission, filterWorkflowPermissions],
        WORKFLOW_PERMISSION_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: filterWorkflowPermissions,
        },
        '',
        {
            enabled: Boolean(workflowArticleTypeId),
            placeholderData: keepPreviousData,
        }
    );
    const workflowPermission = workflowPermissionData?.workflow_permissions_list.data[0];
    const workflowIds = workflowPermission?.workflow_ids;
    const workflowTransitions = workflowPermission?.workflow_transitions;

    const workflowParamConfig: SearchWorkflowParam = omitBy(
        {
            page: Number(page),
            limit: Number(limit),
            ids: workflowIds?.join(','),
        },
        isUndefined
    );

    const filters = generateFilters(workflowParamConfig, workflowFilterConfig);

    const { data: workflowData } = useGraphQLQuery<WorkflowQuery, SearchWorkflow>(
        [QUERY_KEY.WORKFLOWS, workflowParamConfig, filters],
        WORKFLOW_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: Boolean(workflowIds?.length),
            placeholderData: keepPreviousData,
        }
    );
    const workflows = workflowData?.workflows_list.data ?? [];

    return (
        <UpdateArticleForm
            id={id}
            pseudonyms={pseudonyms}
            tags={tags}
            articleTemplates={templates}
            topics={topics}
            categories={categories}
            layouts={layouts}
            workflows={workflows}
            workflowTransitions={workflowTransitions}
            type={type}
            typeId={typeId}
            departmentId={departmentId}
            article={article}
            currentUser={currentUser}
            refetchArticle={refetchArticle}
        />
    );
}
