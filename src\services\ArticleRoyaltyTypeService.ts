import { gql } from 'graphql-request';

export const ARTICLE_ROYALTY_TYPE_LIST = gql`
    query Article_royalty_types_list(
        $page: Int!
        $limit: Int!
        $filters: [String!]
        $sorts: [String!]
        $search: String
    ) {
        article_royalty_types_list(
            body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }
        ) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                article_type_id
                from_royalty
                to_royalty
                is_default
                status_id
            }
        }
    }
`;

export const ARTICLE_ROYALTY_TYPE_CREATE = gql`
    mutation Article_royalty_types_create($body: ArticleRoyaltyTypeSaveInputDto!) {
        article_royalty_types_create(body: $body) {
            id
        }
    }
`;

export const ARTICLE_ROYALTY_TYPE_UPDATE = gql`
    mutation Article_royalty_types_update($id: Int!, $body: ArticleRoyaltyTypeSaveInputDto!) {
        article_royalty_types_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ARTICLE_ROYALTY_TYPE_DELETE = gql`
    mutation Article_royalty_types_delete($id: Int!) {
        article_royalty_types_delete(id: $id)
    }
`;
