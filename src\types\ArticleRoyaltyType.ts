import { baseFilterConfig, BaseModel, Base<PERSON><PERSON>ch, DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';
import { ItemStatus } from './common/Item';

export default interface ArticleRoyaltyType extends BaseModel {
    name: string;
    desc?: string;
    article_type_id: number;
    from_royalty: number;
    to_royalty: number;
    is_default: boolean;
    status_id: ItemStatus | boolean;
}

export interface ArticleRoyaltyTypeListQuery {
    article_royalty_types_list: DataList<ArticleRoyaltyType>;
}

export interface SearchArticleRoyaltyType extends BaseSearch {
    article_type_id?: string;
}

export type SearchArticleRoyaltyTypeParam = {
    [key in keyof SearchArticleRoyaltyType]: string;
};

export const ArticleRoyaltyTypeFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    article_type_id: { key: 'article_type_id', operator: FILTER_CONDITIONS.EQUAL },
};
