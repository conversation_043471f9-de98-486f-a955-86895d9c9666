{"data": [{"responsive_id": null, "invoice_id": 4477, "issued_date": "12/13/2019", "client_name": "Roxy Floodgate", "email": "<EMAIL>", "service": "Software Development", "total": 3428, "avatar": "", "invoice_status": "Paid", "balance": "$724", "due_date": "04/23/2019"}, {"responsive_id": null, "invoice_id": 5020, "issued_date": "07/17/2019", "client_name": "<PERSON>", "email": "roy<PERSON><PERSON><PERSON><PERSON>@email.com", "service": "UI/UX Design & Development", "total": 5219, "avatar": "10-small.png", "invoice_status": "Downloaded", "balance": 0, "due_date": "12/15/2019"}, {"responsive_id": null, "invoice_id": 4506, "issued_date": "10/19/2019", "client_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "service": "Unlimited Extended License", "total": 3719, "avatar": "1-small.png", "invoice_status": "Paid", "balance": 0, "due_date": "11/03/2019"}, {"responsive_id": null, "invoice_id": 4515, "issued_date": "03/06/2020", "client_name": "<PERSON><PERSON>", "email": "kendell<PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Software Development", "total": 4749, "avatar": "9-small.png", "invoice_status": "<PERSON><PERSON>", "balance": 0, "due_date": "02/11/2020"}, {"responsive_id": null, "invoice_id": 4831, "issued_date": "02/08/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "do<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "UI/UX Design & Development", "total": 4056, "avatar": "10-small.png", "invoice_status": "Draft", "balance": "$815", "due_date": "06/30/2019"}, {"responsive_id": null, "invoice_id": 4881, "issued_date": "08/26/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "UI/UX Design & Development", "total": 2771, "avatar": "", "invoice_status": "Paid", "balance": 0, "due_date": "06/24/2019"}, {"responsive_id": null, "invoice_id": 4877, "issued_date": "09/17/2019", "client_name": "<PERSON>", "email": "tudo<PERSON><PERSON><PERSON>@email.com", "service": "UI/UX Design & Development", "total": 2713, "avatar": "", "invoice_status": "Draft", "balance": "$407", "due_date": "11/22/2019"}, {"responsive_id": null, "invoice_id": 4687, "issued_date": "02/11/2020", "client_name": "<PERSON>", "email": "peggy<PERSON><PERSON><PERSON>@email.com", "service": "Template Customization", "total": 4309, "avatar": "9-small.png", "invoice_status": "Paid", "balance": "-$205", "due_date": "02/10/2020"}, {"responsive_id": null, "invoice_id": 4917, "issued_date": "01/26/2020", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Software Development", "total": 3367, "avatar": "2-small.png", "invoice_status": "Downloaded", "balance": 0, "due_date": "12/24/2019"}, {"responsive_id": null, "invoice_id": 4790, "issued_date": "01/15/2020", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Software Development", "total": 4776, "avatar": "9-small.png", "invoice_status": "Downloaded", "balance": "$305", "due_date": "06/02/2019"}, {"responsive_id": null, "invoice_id": 4965, "issued_date": "09/27/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>'Hear", "service": "Unlimited Extended License", "total": 3789, "avatar": "4-small.png", "invoice_status": "Partial Payment", "balance": "$666", "due_date": "03/18/2020"}, {"responsive_id": null, "invoice_id": 4449, "issued_date": "07/31/2019", "client_name": "<PERSON>", "email": "<EMAIL>'<PERSON><PERSON><PERSON>", "service": "Unlimited Extended License", "total": 5200, "avatar": "5-small.png", "invoice_status": "Partial Payment", "balance": 0, "due_date": "01/17/2020"}, {"responsive_id": null, "invoice_id": 4511, "issued_date": "02/14/2020", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Software Development", "total": 4558, "avatar": "7-small.png", "invoice_status": "Paid", "balance": 0, "due_date": "10/01/2019"}, {"responsive_id": null, "invoice_id": 4677, "issued_date": "05/21/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Template Customization", "total": 3503, "avatar": "9-small.png", "invoice_status": "Paid", "balance": 0, "due_date": "05/22/2019"}, {"responsive_id": null, "invoice_id": 5024, "issued_date": "06/30/2019", "client_name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 5285, "avatar": "2-small.png", "invoice_status": "Partial Payment", "balance": "-$202", "due_date": "08/02/2019"}, {"responsive_id": null, "invoice_id": 4743, "issued_date": "06/21/2019", "client_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "britte<PERSON><PERSON><PERSON>@email.com", "service": "UI/UX Design & Development", "total": 3668, "avatar": "6-small.png", "invoice_status": "Downloaded", "balance": "$731", "due_date": "12/15/2019"}, {"responsive_id": null, "invoice_id": 4416, "issued_date": "12/30/2019", "client_name": "<PERSON><PERSON>", "email": "shelly<PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 4372, "avatar": "", "invoice_status": "<PERSON><PERSON>", "balance": "-$344", "due_date": "09/17/2019"}, {"responsive_id": null, "invoice_id": 4943, "issued_date": "05/27/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Template Customization", "total": 3198, "avatar": "7-small.png", "invoice_status": "Partial Payment", "balance": "-$253", "due_date": "08/16/2019"}, {"responsive_id": null, "invoice_id": 4989, "issued_date": "07/30/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Unlimited Extended License", "total": 5293, "avatar": "", "invoice_status": "Past Due", "balance": 0, "due_date": "08/01/2019"}, {"responsive_id": null, "invoice_id": 4582, "issued_date": "06/10/2019", "client_name": "<PERSON><PERSON>", "email": "keaneb<PERSON><PERSON><EMAIL>", "service": "Template Customization", "total": 5612, "avatar": "6-small.png", "invoice_status": "Downloaded", "balance": "$883", "due_date": "04/12/2019"}, {"responsive_id": null, "invoice_id": 5041, "issued_date": "02/01/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "service": "Software Development", "total": 2230, "avatar": "1-small.png", "invoice_status": "<PERSON><PERSON>", "balance": 0, "due_date": "11/19/2019"}, {"responsive_id": null, "invoice_id": 4401, "issued_date": "03/22/2020", "client_name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 2032, "avatar": "8-small.png", "invoice_status": "Partial Payment", "balance": 0, "due_date": "11/30/2019"}, {"responsive_id": null, "invoice_id": 4535, "issued_date": "11/30/2019", "client_name": "<PERSON><PERSON><PERSON>", "email": "igna<PERSON><PERSON><PERSON>@email.com", "service": "UI/UX Design & Development", "total": 3128, "avatar": "3-small.png", "invoice_status": "Paid", "balance": 0, "due_date": "09/10/2019"}, {"responsive_id": null, "invoice_id": 4683, "issued_date": "01/06/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "service": "Software Development", "total": 2060, "avatar": "2-small.png", "invoice_status": "Downloaded", "balance": 0, "due_date": "12/08/2019"}, {"responsive_id": null, "invoice_id": 4410, "issued_date": "06/01/2019", "client_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "service": "UI/UX Design & Development", "total": 4077, "avatar": "", "invoice_status": "Draft", "balance": 0, "due_date": "02/01/2020"}, {"responsive_id": null, "invoice_id": 4716, "issued_date": "10/30/2019", "client_name": "<PERSON><PERSON>", "email": "nine<PERSON><PERSON><PERSON>@email.com", "service": "Template Customization", "total": 2872, "avatar": "4-small.png", "invoice_status": "Partial Payment", "balance": 0, "due_date": "10/18/2019"}, {"responsive_id": null, "invoice_id": 4341, "issued_date": "02/05/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "service": "Software Development", "total": 3740, "avatar": "10-small.png", "invoice_status": "Draft", "balance": 0, "due_date": "11/01/2019"}, {"responsive_id": null, "invoice_id": 4989, "issued_date": "12/01/2019", "client_name": "<PERSON><PERSON>", "email": "lorine<PERSON><PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 3623, "avatar": "", "invoice_status": "Downloaded", "balance": 0, "due_date": "09/23/2019"}, {"responsive_id": null, "invoice_id": 4446, "issued_date": "04/16/2019", "client_name": "<PERSON>", "email": "<PERSON><PERSON><PERSON>@email.com", "service": "Software Development", "total": 2477, "avatar": "7-small.png", "invoice_status": "Draft", "balance": 0, "due_date": "04/01/2019"}, {"responsive_id": null, "invoice_id": 4765, "issued_date": "01/24/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "pry<PERSON><PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 3904, "avatar": "", "invoice_status": "Paid", "balance": "$951", "due_date": "09/30/2019"}, {"responsive_id": null, "invoice_id": 4575, "issued_date": "02/24/2020", "client_name": "<PERSON><PERSON>en", "email": "<EMAIL>", "service": "UI/UX Design & Development", "total": 3102, "avatar": "1-small.png", "invoice_status": "Partial Payment", "balance": "-$153", "due_date": "08/25/2019"}, {"responsive_id": null, "invoice_id": 4538, "issued_date": "02/29/2020", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "UI/UX Design & Development", "total": 2483, "avatar": "5-small.png", "invoice_status": "Draft", "balance": 0, "due_date": "07/10/2019"}, {"responsive_id": null, "invoice_id": 4798, "issued_date": "08/07/2019", "client_name": "<PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 2825, "avatar": "8-small.png", "invoice_status": "Partial Payment", "balance": "-$459", "due_date": "10/14/2019"}, {"responsive_id": null, "invoice_id": 4963, "issued_date": "05/10/2019", "client_name": "Morgan Ewbanks", "email": "<EMAIL>", "service": "Unlimited Extended License", "total": 2029, "avatar": "4-small.png", "invoice_status": "Past Due", "balance": 0, "due_date": "03/28/2019"}, {"responsive_id": null, "invoice_id": 4528, "issued_date": "04/02/2019", "client_name": "<PERSON><PERSON>", "email": "rahal<PERSON><PERSON><PERSON>@email.com", "service": "Software Development", "total": 3208, "avatar": "", "invoice_status": "<PERSON><PERSON>", "balance": 0, "due_date": "09/06/2019"}, {"responsive_id": null, "invoice_id": 5089, "issued_date": "05/02/2019", "client_name": "<PERSON>", "email": "jamalk<PERSON><PERSON>@email.com", "service": "Software Development", "total": 3077, "avatar": "", "invoice_status": "<PERSON><PERSON>", "balance": 0, "due_date": "05/09/2019"}, {"responsive_id": null, "invoice_id": 4456, "issued_date": "03/23/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "claudine<PERSON><PERSON><PERSON>@email.com", "service": "Software Development", "total": 5578, "avatar": "9-small.png", "invoice_status": "Draft", "balance": 0, "due_date": "07/23/2019"}, {"responsive_id": null, "invoice_id": 5027, "issued_date": "09/28/2019", "client_name": "<PERSON><PERSON>", "email": "de<PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Software Development", "total": 2787, "avatar": "1-small.png", "invoice_status": "Partial Payment", "balance": 0, "due_date": "09/25/2019"}, {"responsive_id": null, "invoice_id": 4748, "issued_date": "02/21/2020", "client_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "service": "UI/UX Design & Development", "total": 5591, "avatar": "", "invoice_status": "Downloaded", "balance": 0, "due_date": "06/07/2019"}, {"responsive_id": null, "invoice_id": 4651, "issued_date": "05/24/2019", "client_name": "<PERSON><PERSON>", "email": "jen<PERSON><PERSON><PERSON>@email.com", "service": "Template Customization", "total": 2783, "avatar": "6-small.png", "invoice_status": "Draft", "balance": 0, "due_date": "10/22/2019"}, {"responsive_id": null, "invoice_id": 4794, "issued_date": "01/13/2020", "client_name": "Hephzibah Hanshawe", "email": "he<PERSON><PERSON><PERSON><EMAIL>", "service": "Template Customization", "total": 2719, "avatar": "", "invoice_status": "<PERSON><PERSON>", "balance": 0, "due_date": "02/04/2020"}, {"responsive_id": null, "invoice_id": 4593, "issued_date": "05/18/2019", "client_name": "<PERSON>", "email": "darwind<PERSON>@email.com", "service": "Template Customization", "total": 3325, "avatar": "", "invoice_status": "Draft", "balance": "$361", "due_date": "03/02/2020"}, {"responsive_id": null, "invoice_id": 4437, "issued_date": "10/29/2019", "client_name": "<PERSON><PERSON><PERSON>", "email": "orbadiah<PERSON><PERSON>@email.com", "service": "Template Customization", "total": 3851, "avatar": "", "invoice_status": "Draft", "balance": 0, "due_date": "08/25/2019"}, {"responsive_id": null, "invoice_id": 4632, "issued_date": "04/07/2019", "client_name": "<PERSON><PERSON><PERSON>", "email": "eadith<PERSON><PERSON>@email.com", "service": "Template Customization", "total": 5565, "avatar": "", "invoice_status": "Draft", "balance": 0, "due_date": "03/06/2020"}, {"responsive_id": null, "invoice_id": 4995, "issued_date": "08/21/2019", "client_name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Template Customization", "total": 3313, "avatar": "3-small.png", "invoice_status": "Partial Payment", "balance": 0, "due_date": "06/09/2019"}, {"responsive_id": null, "invoice_id": 4375, "issued_date": "05/31/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Template Customization", "total": 5181, "avatar": "", "invoice_status": "Partial Payment", "balance": 0, "due_date": "10/22/2019"}, {"responsive_id": null, "invoice_id": 4323, "issued_date": "07/12/2019", "client_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Template Customization", "total": 2869, "avatar": "1-small.png", "invoice_status": "Partial Payment", "balance": 0, "due_date": "03/22/2020"}, {"responsive_id": null, "invoice_id": 4993, "issued_date": "07/10/2019", "client_name": "<PERSON><PERSON>", "email": "luter<PERSON><PERSON><PERSON><PERSON><PERSON>@email.com", "service": "Unlimited Extended License", "total": 4836, "avatar": "", "invoice_status": "Partial Payment", "balance": 0, "due_date": "10/22/2019"}, {"responsive_id": null, "invoice_id": 4439, "issued_date": "07/20/2019", "client_name": "<PERSON>", "email": "<EMAIL>", "service": "UI/UX Design & Development", "total": 4263, "avatar": "", "invoice_status": "Draft", "balance": "$762", "due_date": "06/12/2019"}, {"responsive_id": null, "invoice_id": 4567, "issued_date": "04/19/2019", "client_name": "<PERSON><PERSON>", "email": "<EMAIL>", "service": "Unlimited Extended License", "total": 3171, "avatar": "9-small.png", "invoice_status": "Draft", "balance": "-$205", "due_date": "09/25/2019"}]}