import { FILTER_CONDITIONS } from '../constants/common';
import { CategoryItem } from '../features/article/components/CategoryListCheckbox';
import ArticleCategory from './ArticleCategory';
import ArticleFile from './ArticleFile';
import ArticleLog from './ArticleLog';
import ArticleNote from './ArticleNote';
import ArticleTag from './ArticleTag';
import { BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { FileResponse, ItemParam, SelectOption } from './common/Item';
import Department from './Department';
import Issue from './Issue';
import Layout from './Layout';
import { PressPublication } from './PressPublication';
import Pseudonym from './Pseudonym';
import RelatedArticle from './RelatedArticle';
import User from './User';
import Workflow from './Workflow';

export enum ArticlePageType {
    PERSONAL = 'personal',
    REPORTER = 'reporter',
    COLLABORATOR = 'collaborator',
}

export enum TypeSettingStatus {
    WAIT = 1,
    DOING = 2,
    DONE = 3,
}

export const TypeSettingStatusNames: ItemParam[] = [
    { id: TypeSettingStatus.WAIT, name: 'Chờ dàn trang' },
    { id: TypeSettingStatus.DOING, name: 'Đang dàn trang' },
    { id: TypeSettingStatus.DONE, name: 'Đã dàn trang' },
];

export interface ArticleFormData {
    title: string;
    sub_title: string;
    brief_title: string;
    slug: string;
    desc: string;
    content: string;
    avatar1_id: number | null;
    avatar2_id: number | null;
    workflow_id: number;
    article_type_id: number;
    pseudonym_id: number | null;
    pseudonym_name?: string;
    web_layout_id: number;
    mobile_layout_id: number;
    department_id: number;
    publish_date: string | null;
    source: string;
    file_id: number;
    root_article_id: number | null;
    article_categories?: CategoryItem[]; //mainCategoryIds + subCategoryIds + topicId
    article_tags?: string[]; //tagAdds
    article_kind_ids?: number[]; //selectedArticleKindIds
    new_article_notes?: string[]; //newArticleNotes
    is_sync?: boolean;
    article_type_copy_ids?: number[];
    related_article_ids?: number[];
    press_publication_id?: number;
    issue_id?: number;
    article_issue_pages?: ArticleIssueInput[];
    typesetting_status_id?: TypeSettingStatus;
    is_unclassified?: boolean;
    article_royalties: ArticleRoyaltiesPage[];
}

export default interface Article extends BaseModel, ArticleFormData {
    lock_user_id?: number;
    lock_at?: string;
    avatar1: FileResponse;
    avatar2: FileResponse;
    workflow: Workflow;
    pseudonym: Pseudonym;
    layout: Layout;
    department: Department;
    file: FileResponse;
    rootArticle: Article | null;
    childrenArticles: Article[];
    lockUser: User;
    articleCategories: ArticleCategory[];
    articleTags: ArticleTag[];
    articleFiles: ArticleFile[];
    articleNotes: ArticleNote[];
    articleLogs: ArticleLog[];
    articleKinds: { article_id: number; article_kind_id: number }[];
    relatedArticles: RelatedArticle[];
    articleIssuePages: ArticleIssuePage[];
    articleRoyalties: ArticleRoyaltiesPage[];
    pressPublication: PressPublication;
    issue: Issue;
    from_date?: string;
    to_date?: string;
}

export interface ArticleQueryRes {
    articles_list: DataList<Article>;
}

export interface SearchArticle extends BaseSearch {
    workflow_id?: string;
    title?: string;
    article_type_id?: number;
    created_by?: string;
    id?: string;
    root_article_id?: string;
    is_unclassified?: string;
    from_date?: string;
    to_date?: string;
    created_at_from?: string;
    created_at_to?: string;
    created_at__range?: string;
    created_group_id?: string;
}

export type SearchArticleParam = {
    [key in keyof SearchArticle]: string;
};

export const articleFilterConfig: FilterConfig = {
    workflow_id: { key: 'workflow_id', operator: FILTER_CONDITIONS.IN },
    title: { key: 'title', operator: FILTER_CONDITIONS.LIKE },
    article_type_id: { key: 'article_type_id', operator: FILTER_CONDITIONS.EQUAL },
    created_by: { key: 'created_by', operator: FILTER_CONDITIONS.IN },
    root_article_id: { key: 'root_article_id', operator: FILTER_CONDITIONS.EQUAL },
    is_unclassified: { key: 'is_unclassified', operator: FILTER_CONDITIONS.EQUAL },
    from_date: { key: 'from_date', operator: FILTER_CONDITIONS.GREATER_THAN },
    to_date: { key: 'to_date', operator: FILTER_CONDITIONS.LESS_THAN },
    created_at_from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at_to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
    department_id: { key: 'department_id', operator: FILTER_CONDITIONS.EQUAL },
    created_group_id: { key: 'createdByUser.groups.id', operator: FILTER_CONDITIONS.EQUAL },
};

export enum ArticleKind {
    IMAGE = 1,
    VIDEO = 2,
    AUDIO = 3,
    E_MAGAZINE = 4,
    INFOGRAPHIC = 5,
    LIVE = 6,
    TEXT_TO_SPEECH = 7,
}

export const ArticleKindNames: ItemParam[] = [
    { id: ArticleKind.IMAGE, name: 'Tin ảnh' },
    { id: ArticleKind.E_MAGAZINE, name: 'Tin eMagazine' },
    { id: ArticleKind.VIDEO, name: 'Tin video' },
    { id: ArticleKind.INFOGRAPHIC, name: 'Tin Infographic' },
    { id: ArticleKind.AUDIO, name: 'Tin audio' },
    { id: ArticleKind.LIVE, name: 'Tin trực tiếp' },
    { id: ArticleKind.TEXT_TO_SPEECH, name: 'Tin Text to speech' },
];

export interface DeleteArticleRes {
    articles_delete: boolean;
}

export interface ArticleDetailQueryRes {
    articles_detail: Article;
}

export enum NewArticleType {
    ALL = 'all',
    UNCLASSIFIED = 'null',
}

export enum PaperSize {
    A3 = 'A3',
    A4 = 'A4',
}

export const PaperSizeOptions = [
    { value: PaperSize.A3, label: 'A3' },
    { value: PaperSize.A4, label: 'A4' },
];

export interface ArticleBox {
    id: string | number; // string for temp client IDs, number for DB IDs
    articleId: number | undefined;
    article: Article;
    x: number;
    y: number;
    width: number;
    height: number;
    isOverflowing: boolean;
    isTemporary?: boolean; // true for client-side temp IDs
}

export const PAPER_DIMENSIONS = {
    [PaperSize.A3]: { width: 842, height: 1191 }, // A3 in pixels (scaled down)
    [PaperSize.A4]: { width: 595, height: 842 }, // A4 in pixels (scaled down)
};

export const SCALE_FACTOR = 1; // Scale down for display

// Utility functions for ArticleBox ID management
export const createTempBoxId = (articleId: number): string => `temp-${Date.now()}-${articleId}`;

export const isTemporaryId = (id: string | number): boolean => typeof id === 'string' && id.startsWith('temp-');

export const getBoxDisplayId = (box: ArticleBox): string => (box.isTemporary ? (box.id as string) : `box-${box.id}`);

export interface ArticleIssuePage extends BaseModel {
    article_id: number;
    issue_page_id: number;
    display_order: number;
    comment: string;
    article: Article;
    issuePage: Issue;
    position: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | null;
}

export interface ArticleIssueInput {
    issue_page_id: number;
    display_order: number;
    comment?: string;
}

export enum PlanTypes {
    PAPER = 1,
    ORGANIZATION = 2,
    WAITING = 3,
    APPROVE = 4,
}

export const PlanTypesName = [
    { id: PlanTypes.PAPER, name: 'Kế hoạch trang' },
    { id: PlanTypes.ORGANIZATION, name: 'Tổ chức trang' },
    { id: PlanTypes.WAITING, name: 'Chờ dàn trang' },
    { id: PlanTypes.APPROVE, name: 'Duyệt trang báo' },
];

export enum RoyaltyType {
    Content = 1,
    Media,
    Support,
}
export const RoyaltyTypeOptions = [
    {
        label: 'Biên tập',
        value: RoyaltyType.Content,
    },
    {
        label: 'Media',
        value: RoyaltyType.Media,
    },
    {
        label: 'Hỗ trợ',
        value: RoyaltyType.Support,
    },
];

export interface ArticleRoyaltiesPage extends BaseModel {
    royalty_type_id?: number;
    suggest_royalty: number;
    type_id: RoyaltyType;
    article_royalty_users: ArticleRoyaltyUser[];
    articleRoyaltyUsers?: ArticleRoyaltyUser[];
}

export const defaultArticleRoyalties: ArticleRoyaltiesPage[] = [
    {
        article_royalty_users: [],
        type_id: RoyaltyType.Content,
        suggest_royalty: 0,
    },
    {
        article_royalty_users: [],
        type_id: RoyaltyType.Media,
        suggest_royalty: 0,
    },
    {
        article_royalty_users: [],
        type_id: RoyaltyType.Support,
        suggest_royalty: 0,
    },
];

export interface ArticleRoyaltyUser extends BaseModel {
    article_royalty_id: number;
    user_id: number;
    percent: number;
    comment: string;
    param_config: { label: number; value: string }[];
    final_royalty: number;
}

export interface ArticleRoyalty extends BaseModel {
    article_id: number;
    type_id: RoyaltyType;
    suggest_royalty: number;
    royalty_type_id?: number;
    articleRoyaltyUsers: ArticleRoyaltyUser[];
}

export interface ArticleRoyaltiesQueryRes {
    article_royalties_list: DataList<ArticleRoyalty>;
}
