import { Star, Trash2 } from 'react-feather';
import { genTableIndex, getFieldHtml } from 'utils/common';
import { useTranslation } from 'react-i18next';
import ArticleRoyaltyType from 'types/ArticleRoyaltyType';
import { ArticleTypeNames, ItemStatusNames } from '../../../types/common/Item';
import FormatNumber from '../../../components/partials/FormatNumber';
import { Paging } from '../../../types/common';

interface IProps {
    items: ArticleRoyaltyType[];
    paging: Paging;
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListArticleRoyaltyType({ items, paging, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Tên</th>
                        <th className="text-center"><PERSON><PERSON>i</th>
                        <th className="text-center"><PERSON><PERSON><PERSON><PERSON> đinh mức</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="text-center">Mặc định</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item, index) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">{getFieldHtml(ArticleTypeNames, item.article_type_id, t)}</td>
                            <td className="text-center">
                                <FormatNumber value={item.from_royalty} isInput={false} renderText={(value) => value} />{' '}
                                - <FormatNumber value={item.to_royalty} isInput={false} renderText={(value) => value} />
                            </td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                {item.is_default && <Star size={16} className="text-warning" />}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
