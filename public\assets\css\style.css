html .content.app-content {
    min-height: calc(100vh - 50px);
}

.dropdown-menu.show {
    position: absolute;
    inset: 0px auto auto 0px;
    margin: 0px;
}

.main-menu .shadow-bottom.onScrollMenu {
    display: block;
}

.main-menu .navbar-header {
    height: 75px;
}

.main-menu .navbar-header .navbar-brand .brand-logo img {
    max-width: 189px;
    max-height: 40px;
    margin-left: 60px;
}

.MuiTabs-scroller.MuiTabs-fixed {
    max-width: 100%;
    overflow: auto !important;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar {
    width: 4px;
    height: 8px;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar:hover {
    width: 4px;
    height: 8px;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 4px;
    height: 8px;
    background-color: #ccc;
}

.bg-warning .card-body {
    padding: 0.5rem 0.5rem;
}

.cursor-pointer {
    cursor: pointer;
}

span.error {
    color: #ea5455;
}

.input-group-text-readOnly {
    background-color: #efefef;
}

.no-border-lr {
    border-left: none !important;
    border-right: none !important;
}

.badge-balance {
    margin: 0 0.5rem;
}

.checkbox-btn {
    margin-right: 5px;
}

.btn-send-group {
    display: flex;
    flex-direction: column;
}

button.color-primary:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.thAction1 {
    width: 50px;
}

.thAction2 {
    width: 100px;
}

.thAction3 {
    width: 130px;
}

.thWidth150 {
    width: 150px;
}

.wh-14 {
    width: 14px !important;
    height: 14px !important;
}

.h100px {
    height: 100px !important;
}

.text-heading {
    --bs-text-opacity: 1;
    color: #444050 !important;
}

.border-none {
    border-style: none;
}

.bootstrap-touchspin.input-group {
    margin: 0 auto;
}

.form-check-input:disabled {
    background-color: #efefef;
    border-color: #efefef;
}

.form-check-input:disabled:checked {
    background-color: #a42c48ff !important;
    border-color: #a42c48ff !important;
}

.object-fit-contain {
    object-fit: contain;
}

.MuiTab-textColorPrimary.Mui-selected {
    color: #a42c48ff !important;
}

.MuiTabs-indicator {
    background-color: #a42c48ff !important;
}

.react-tags {
    display: flex;
    flex-direction: column-reverse;
    gap: 10px
}

.react-tags>.react-tags__search>.react-tags__search-wrapper input {
    width: 100% !important;
    height: 30px;
    outline: none;
    border: 1px solid #ced4da;
    border-radius: 5px;
    padding: 0 10px;
}

.react-tags__suggestions>ul>li {
    cursor: pointer;
}

.react-tags__suggestions>ul>li:hover {
    background-color: #fcf8e3;
}

.react-tags>.react-tags__selected>button {
    background: #FCF3F5FF;
    padding: 3px 5px;
    margin-bottom: 5px;
    border-radius: 5px;
    height: 100%;
    margin-right: 10px;
    color: #a42c48ff;
}

.react-tags__selected-tag {
    display: inline-flex;
    align-items: center;
    background-color: #fdecef;
    color: #8a2c42;
    border-radius: 9999px;
    padding: 4px 8px;
    font-size: 14px;
    margin: 4px;
    border: none;
    position: relative;
}

.react-tags__selected-tag-name {
    margin-right: 6px;
}

.react-tags__selected-tag::after {
    content: 'x';
    font-size: 16px;
    font-weight: bold;
    color: #8a2c42;
    cursor: pointer;
    margin-left: 5px;
    line-height: 1;
    display: inline-block;
    transform: translateY(1px);
}