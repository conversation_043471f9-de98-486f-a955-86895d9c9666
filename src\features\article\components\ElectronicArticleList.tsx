import { Edit, PenTool, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Article, { ArticlePageType } from '../../../types/Article';
import ArticleCategory from '../../../types/ArticleCategory';
import { CategoryType } from '../../../types/Category';
import { Paging } from '../../../types/common';
import { ArticleTypeNames } from '../../../types/common/Item';
import { genTableIndex } from '../../../utils/common';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';

interface IProps {
    articles: Article[];
    paging: Paging;
    onDelete: (id: number) => void;
    onOpenRoyaltyModal?: (article: Article) => void;
    acticlePageType: ArticlePageType;
    isRoyaltiesPage?: boolean;
}

export default function ElectronicArticleList({
    articles,
    paging,
    onDelete,
    acticlePageType,
    isRoyaltiesPage = false,
    onOpenRoyaltyModal,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const renderActionColumn = (article: Article) => {
        if (isRoyaltiesPage) {
            return (
                <div className="d-flex justify-content-center">
                    <button
                        className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                        title="Chấm nhuận bút"
                        onClick={() => onOpenRoyaltyModal?.(article)}
                    >
                        <PenTool size={14} />
                    </button>
                </div>
            );
        }

        return (
            <div className="d-flex justify-content-center">
                <Link
                    to={`/article/edit/${acticlePageType}/${article.article_type_id}/${article.id}`}
                    className="btn btn-icon btn-sm btn-flat-primary waves-effect me-1"
                    title="Xem"
                >
                    <Edit size={14} />
                </Link>
                <button
                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                    title="Xóa"
                    onClick={() => onDelete(article.id!)}
                >
                    <Trash2 size={14} />
                </button>
            </div>
        );
    };

    return (
        <tbody>
            {articles.map((article, index) => (
                <tr key={article.id}>
                    <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                    <td>
                        <div>{article.title}</div>
                        <div className="text-muted small">{article.pseudonym?.name || 'Chưa có bút danh'}</div>
                        <div className="text-muted small">
                            {article.publish_date
                                ? formatDateTime(article.publish_date, FORMAT_DATE.SHOW_DATE_TIME)
                                : 'Chưa có ngày đăng'}
                        </div>
                    </td>
                    <td>
                        {t(
                            `${
                                ArticleTypeNames.find((type) => type.id === article.article_type_id)?.name ||
                                'unclassified'
                            }.single`
                        )}
                    </td>
                    <td>
                        {article.articleCategories && article.articleCategories.length > 0 ? (
                            article.articleCategories
                                .filter(
                                    (ac: ArticleCategory) => ac.category?.category_type_id === CategoryType.CATEGORY
                                )
                                .map((ac: ArticleCategory) => <div key={ac.id}>{ac.category?.name}</div>)
                        ) : (
                            <div className="text-muted small">Chưa có chuyên mục</div>
                        )}
                    </td>
                    <td>
                        <div>{article.updatedByUser?.full_name || article.createdByUser?.full_name}</div>
                        <div className="text-muted small">
                            Ngày biên soạn:{' '}
                            {formatDateTime(article.updated_at || article.created_at!, FORMAT_DATE.SHOW_DATE_TIME)}
                        </div>
                    </td>
                    <td className="text-center">
                        <span
                            style={{ backgroundColor: article.workflow?.desc || '#000000' }}
                            className={`text-[#fff] badge `}
                        >
                            {article.workflow?.name || 'Chưa có trạng thái'}
                        </span>
                    </td>
                    <td className="text-center">{renderActionColumn(article)}</td>
                </tr>
            ))}
        </tbody>
    );
}
