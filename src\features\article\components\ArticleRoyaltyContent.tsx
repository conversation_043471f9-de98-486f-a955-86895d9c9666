import { keepPreviousData } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import UpdateButton from 'components/partials/UpdateButton';
import { LIMIT_MAX, OPERATION_NAME, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { isEmpty, isUndefined, omitBy } from 'lodash';
import { useEffect, useMemo } from 'react';
import { Plus } from 'react-feather';
import { FormProvider, useForm } from 'react-hook-form';
import { ARTICLES_DETAIL } from 'services/ArticleService';
import { ROYALTY_PARAMS_LIST } from 'services/RoyaltyParam';
import { USER_LIST } from 'services/UserService';
import { useAppStore } from 'stores/appStore';
import { ArticleDetailQueryRes, ArticleRoyaltiesPage, RoyaltyTypeOptions } from 'types/Article';
import { baseFilterConfig } from 'types/common';
import { ItemStatus } from 'types/common/Item';
import { RoyaltyParamsQuery } from 'types/RoyaltyParams';
import { UserListQuery } from 'types/User';
import { generateFilters } from 'utils/common';
import CommentRoyaltyInputCell from './CommentRoyaltyInputCell';
import FinalRoyaltyInputCell from './FinalRoyaltyInputCell';
import RoyaltyActionCell from './RoyaltyActionCell';
import ParamRoyaltyInputCell from './RoyaltyParamInputCell';
import UserRoyaltyInputCell from './UserRoyaltyInputCell';

type Props = {
    articleId: number;
};

type FormValues = {
    article_royalties: ArticleRoyaltiesPage[];
};

const ArticleRoyaltyContent = ({ articleId }: Props) => {
    const departmentId = useAppStore((state) => state.departmentId);

    const form = useForm<FormValues>({
        defaultValues: {
            article_royalties: [],
        },
    });

    const { control, handleSubmit, reset, watch } = form;

    const { data: articleData } = useGraphQLQuery<ArticleDetailQueryRes, { id: number }>(
        [QUERY_KEY.ARTICLE, articleId],
        ARTICLES_DETAIL,
        { id: Number(articleId) },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!articleId,
        }
    );
    const articleRoyalties = useMemo(() => articleData?.articles_detail?.articleRoyalties || [], [articleData]);

    const paramConfig = omitBy(
        {
            limit: LIMIT_MAX,
            page: 1,
            status_id: ItemStatus.ACTIVE,
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, baseFilterConfig);

    const { data: royaltyParamsData } = useGraphQLQuery<RoyaltyParamsQuery>(
        [QUERY_KEY.ROYALTY_PARAMS, paramConfig, filters],
        ROYALTY_PARAMS_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );

    const royaltyParams = useMemo(() => royaltyParamsData?.royalty_params_list?.data || [], [royaltyParamsData]);

    const { data: userData } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS],
        USER_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const userOptions = useMemo(
        () =>
            userData?.users_list?.data?.map((u) => ({
                label: u.full_name,
                value: u.id || 0,
            })) || [],
        [userData]
    );

    const dynamicColumns: ColumnDef<ArticleRoyaltiesPage>[] = useMemo(() => {
        return royaltyParams.map((param) => {
            const options = param.values.map((item) => ({
                label: item.name,
                value: Number(item.value),
            }));
            return {
                header: param.name,
                id: param.name,
                accessorKey: param.name,
                cell: ({ row }) => <ParamRoyaltyInputCell row={row} options={options} />,
            };
        });
    }, [royaltyParams]);

    const columns: ColumnDef<ArticleRoyaltiesPage>[] = useMemo(
        () => [
            {
                header: () => <div className="text-center">STT</div>,
                accessorKey: 'index',
                cell: ({ row }) => <div className="text-center">{row.index + 1}</div>,
                size: 48,
            },
            {
                header: 'Nhóm',
                accessorKey: 'type_id',
                cell: ({ row }) => RoyaltyTypeOptions.find((o) => o.value === row.original.type_id)?.label,
                size: 80,
            },
            {
                header: 'Đề xuất',
                accessorKey: 'suggest_royalty',
                cell: ({ row }) => <div className="text-center">{row.original.suggest_royalty}</div>,
                size: 60,
            },
            {
                id: 'articleRoyaltyUsers',
                header: () => <div className="text-center">Người thực hiện</div>,
                columns: [
                    {
                        header: 'Người thực hiện',
                        cell: ({ row }) => <UserRoyaltyInputCell row={row} options={userOptions} />,
                    },
                    ...(!isEmpty(royaltyParams) ? dynamicColumns : []),
                    {
                        header: 'Điểm',
                        cell: ({ row }) => <FinalRoyaltyInputCell row={row} />,
                    },
                    {
                        header: 'Ghi chú',
                        cell: ({ row }) => <CommentRoyaltyInputCell row={row} />,
                    },
                    {
                        id: 'action',
                        header: () => (
                            <div className="text-center">
                                <button
                                    onClick={() => {}}
                                    type="button"
                                    title="Thêm"
                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                >
                                    <Plus size={14} />
                                </button>
                            </div>
                        ),
                        cell: ({ row }) => <RoyaltyActionCell row={row} />,
                        size: 48,
                    },
                ],
            },
        ],
        [royaltyParams, dynamicColumns, userOptions]
    );

    const table = useReactTable({
        data: watch('article_royalties') || [],
        columns,
        getCoreRowModel: getCoreRowModel(),
    });

    useEffect(() => {
        if (isEmpty(articleRoyalties)) return;

        reset({
            article_royalties: articleRoyalties,
        });
    }, [articleRoyalties, reset]);

    return (
        <FormProvider {...form}>
            <div className="card-body">
                <div className="table-responsive mb-2">
                    <table className="table">
                        <thead>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <tr key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <th
                                            className="!px-[12px]"
                                            key={header.id}
                                            colSpan={header.colSpan}
                                            style={{ width: header.column.columnDef.size }}
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </th>
                                    ))}
                                </tr>
                            ))}
                        </thead>
                        <tbody>
                            {table.getRowModel().rows.map((row) => (
                                <tr key={row.id}>
                                    {row.getVisibleCells().map((cell) => (
                                        <td
                                            className="!px-[12px]"
                                            style={{ width: cell.column.columnDef.size }}
                                            key={cell.id}
                                        >
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div>
                    <UpdateButton btnText="Cập nhật" />
                </div>
            </div>
        </FormProvider>
    );
};

export default ArticleRoyaltyContent;
