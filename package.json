{"name": "mediasoft", "version": "0.1.0", "private": true, "dependencies": {"@bmunozg/react-image-area": "^1.1.0", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^7.0.0", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@hookform/resolvers": "^2.8.8", "@mui/icons-material": "^7.1.0", "@mui/lab": "^5.0.0-alpha.84", "@mui/material": "^5.8.1", "@tanstack/react-query": "^5.40.0", "@tanstack/react-query-devtools": "^5.40.0", "@tanstack/react-table": "^8.21.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@tinymce/miniature": "^6.0.0", "@tinymce/tinymce-react": "^6.1.0", "@vitejs/plugin-react": "^4.3.1", "apexcharts": "^4.7.0", "axios": "^1.8.2", "classnames": "^2.3.1", "date-fns": "^2.28.0", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "i18next": "^23.11.5", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "nanoid": "5.0.9", "react": "^18.1.0", "react-apexcharts": "^1.4.1", "react-datepicker": "^7.4.0", "react-dom": "^18.1.0", "react-feather": "^2.0.10", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.30.0", "react-i18next": "^14.1.2", "react-image-crop": "^11.0.10", "react-input-color": "^4.0.1", "react-json-view-lite": "^2.4.1", "react-number-format": "^4.9.3", "react-pdf-highlighter": "^8.0.0-rc.0", "react-perfect-scrollbar": "^1.5.8", "react-range-slider-input": "^3.2.1", "react-router-dom": "^6.3.0", "react-select": "^5.7.4", "react-sortablejs": "^6.1.4", "react-tag-autocomplete": "^6.1.0", "react-to-print": "^2.14.13", "react-toastify": "^10.0.5", "reactstrap": "^9.2.3", "rsuite": "^5.83.2", "slugify": "^1.6.6", "sortablejs": "^1.15.6", "tailwind-merge": "^3.3.0", "tinymce": "^7.8.0", "typescript": "^4.4.2", "uuid": "^11.1.0", "vite": "^6.3.5", "vite-tsconfig-paths": "^4.3.2", "web-vitals": "^2.1.0", "yup": "^0.32.11", "zustand": "^4.5.4"}, "scripts": {"dev": "vite", "build": "tslint -c tslint.json \"src/**/*.{ts,tsx}\" && eslint \"src/**/*.{ts,tsx}\" && tsc && vite build", "test": "vite test", "eject": "vite eject", "start": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,tsx,ts,json}\"", "prettier": "prettier --list-different \"src/**/*.{js,jsx,tsx,ts}\"", "tslint": "tslint -c tslint.json \"src/**/*.{ts,tsx}\"", "lint": "eslint \"src/**/*.{ts,tsx}\"", "validate": "prettier --list-different \"src/**/*.{js,jsx,tsx,ts}\" && tslint -c tslint.json \"src/**/*.{ts,tsx}\" && eslint \"src/**/*.{ts,tsx}\"", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@redux-devtools/extension": "^3.3.0", "@tanstack/eslint-plugin-query": "^5.35.6", "@types/jest": "^27.0.1", "@types/jszip": "^3.4.1", "@types/lodash": "^4.14.182", "@types/node": "^22.15.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "^5.3.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^5.28.0", "@typescript-eslint/parser": "^5.28.0", "autoprefixer": "^10.4.21", "eslint": "^8.17.0", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^8.0.0", "lint-staged": "^13.0.2", "postcss": "^8.4.32", "prettier": "^2.6.2", "tailwindcss": "3", "tslint": "^6.1.3"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["yarn tslint -c tslint.json", "yarn eslint", "yarn prettier --write"]}}