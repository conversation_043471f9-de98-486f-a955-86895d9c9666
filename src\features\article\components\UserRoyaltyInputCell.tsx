import { Row } from '@tanstack/react-table';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import Select from 'react-select';
import { ArticleRoyaltiesPage } from 'types/Article';
import { SelectOption } from 'types/common/Item';

const UserRoyaltyInputCell = ({ row, options }: { row: Row<ArticleRoyaltiesPage>; options: SelectOption[] }) => {
    const { control } = useFormContext();

    const { fields } = useFieldArray({
        control,
        name: `article_royalties.${row.index}.articleRoyaltyUsers`,
    });
    return (
        <div className="d-flex flex-column gap-1">
            {fields.map((p, idx) => (
                <Controller
                    name={`article_royalties.${row.index}.articleRoyaltyUsers.${idx}.user_id`}
                    key={p.id}
                    control={control}
                    render={({ field }) => (
                        <Select
                            key={p.id}
                            options={options}
                            value={options.find((opt) => opt.value === field.value)}
                            onChange={(option) => field.onChange(option?.value)}
                            menuPortalTarget={document.body}
                            styles={{
                                menuPortal(base) {
                                    return {
                                        ...base,
                                        zIndex: 1360,
                                    };
                                },
                            }}
                        />
                    )}
                />
            ))}
        </div>
    );
};

export default UserRoyaltyInputCell;
