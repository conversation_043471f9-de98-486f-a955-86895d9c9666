import { Row } from '@tanstack/react-table';
import { Trash2 } from 'react-feather';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ArticleRoyaltiesPage } from 'types/Article';

const RoyaltyActionCell = ({ row }: { row: Row<ArticleRoyaltiesPage> }) => {
    const { control } = useFormContext();
    {
        const { fields, remove } = useFieldArray({
            control,
            name: `article_royalties.${row.index}.articleRoyaltyUsers`,
        });
        return (
            <div className="d-flex flex-column gap-1 align-items-center">
                {fields.map((p, idx) => (
                    <button
                        key={p.id}
                        onClick={() => remove(idx)}
                        type="button"
                        title="Xoá"
                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                    >
                        <Trash2 size={14} />
                    </button>
                ))}
            </div>
        );
    }
};

export default RoyaltyActionCell;
