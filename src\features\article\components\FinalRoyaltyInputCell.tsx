import { Row } from '@tanstack/react-table';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { ArticleRoyaltiesPage } from 'types/Article';

const FinalRoyaltyInputCell = ({ row }: { row: Row<ArticleRoyaltiesPage> }) => {
    const { control } = useFormContext();

    const { fields } = useFieldArray({
        control,
        name: `article_royalties.${row.index}.articleRoyaltyUsers`,
    });
    return (
        <div className="d-flex flex-column gap-1">
            {fields.map((p, idx) => (
                <Controller
                    name={`article_royalties.${row.index}.articleRoyaltyUsers.${idx}.final_royalty`}
                    key={p.id}
                    control={control}
                    render={({ field }) => (
                        <input className="form-control" value={field.value} onChange={field.onChange} />
                    )}
                />
            ))}
        </div>
    );
};

export default FinalRoyaltyInputCell;
