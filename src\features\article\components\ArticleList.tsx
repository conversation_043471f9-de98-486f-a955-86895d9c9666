import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import ModalContent from 'components/partials/ModalContent';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Table } from 'reactstrap';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import Spinner from '../../../components/partials/Spinner';
import { COMMON_MESSAGE } from '../../../constants/common';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { ARTICLES_DELETE } from '../../../services/ArticleService';
import Article, { ArticlePageType, ArticleQueryRes, NewArticleType, SearchArticleParam } from '../../../types/Article';
import { ArticleType } from '../../../types/common/Item';
import { convertPaging, showToast } from '../../../utils/common';
import AllArticleList from './AllArticleList';
import ArticleRoyaltyContent from './ArticleRoyaltyContent';
import ElectronicArticleList from './ElectronicArticleList';

interface IProps {
    activeTab: string;
    displayOptions: Record<string, boolean>;
    isLoading: boolean;
    data: ArticleQueryRes;
    limit: number;
    refetch: (options?: RefetchOptions) => Promise<QueryObserverResult<ArticleQueryRes, Error>>;
    type: ArticlePageType;
    articleTypeId: number;
    isRoyaltiesPage?: boolean;
}

export default function ArticleList({
    activeTab,
    displayOptions,
    isLoading,
    data,
    limit,
    refetch,
    type,
    articleTypeId,
    isRoyaltiesPage = false,
}: Readonly<IProps>) {
    const navigate = useNavigate();
    const articles = data?.articles_list?.data || [];
    const paging = convertPaging<Article, SearchArticleParam>(data.articles_list, limit);
    const [showModalDelete, setShowModalDelete] = useState(false);
    const [articleId, setArticleId] = useState(0);
    const [isOpenRoyaltyModal, setIsOpenRoyaltyModal] = useState(false);
    const [articleSelected, setArticleSelected] = useState<Article | null>(null);

    const { mutate: deleteArticleMutation, isPending: isDeleting } = useGraphQLMutation(ARTICLES_DELETE, '', {
        onSuccess: () => {
            showToast(true, [COMMON_MESSAGE.SUCCESS_MESSAGE]);
            refetch();
        },
        onError: () => {
            showToast(false, [COMMON_MESSAGE.ERROR_MESSAGE]);
        },
    });

    const onDelete = (id: number) => {
        setArticleId(id);
        setShowModalDelete(true);
    };

    const deleteItem = () => {
        deleteArticleMutation({ id: articleId });
        setShowModalDelete(false);
    };

    const onOpenRoyaltyModal = (article: Article) => {
        setIsOpenRoyaltyModal(true);
        setArticleId(article.id!);
        setArticleSelected(article);
    };

    const onCloseRoyaltyModal = (isOpen: boolean) => {
        setIsOpenRoyaltyModal(isOpen);
        setArticleId(0);
        setArticleSelected(null);
    };

    const renderTableBody = () => {
        if (articles.length === 0) {
            return (
                <tbody>
                    <tr>
                        <td colSpan={7}>Không có dữ liệu để hiển thị</td>
                    </tr>
                </tbody>
            );
        }

        if (activeTab === NewArticleType.ALL || activeTab === NewArticleType.UNCLASSIFIED) {
            return (
                <AllArticleList
                    articles={articles}
                    paging={paging}
                    onDelete={onDelete}
                    acticlePageType={type}
                    isRoyaltiesPage={isRoyaltiesPage}
                    onOpenRoyaltyModal={onOpenRoyaltyModal}
                />
            );
        }

        if (
            activeTab === ArticleType.ELECTRONIC.toString() ||
            activeTab === ArticleType.PAPER.toString() ||
            activeTab === ArticleType.TELEVISION.toString() ||
            activeTab === ArticleType.VIDEO.toString()
        ) {
            return (
                <ElectronicArticleList
                    articles={articles}
                    paging={paging}
                    onDelete={onDelete}
                    acticlePageType={type}
                    isRoyaltiesPage={isRoyaltiesPage}
                    onOpenRoyaltyModal={onOpenRoyaltyModal}
                />
            );
        }

        // Fallback cho các tab khác
        return (
            <tbody>
                <tr>
                    <td colSpan={7}>Không có dữ liệu để hiển thị</td>
                </tr>
            </tbody>
        );
    };

    const renderTableUI = () => (
        <Table responsive striped>
            <thead>
                <tr>
                    <th className="text-center">STT</th>
                    <th>Tiêu đề</th>
                    <th>Thể loại tin</th>
                    <th>Chuyên mục</th>
                    <th>Người biên tập</th>
                    <th className="text-center">Trạng thái</th>
                    <th className="text-center">Xử lý</th>
                </tr>
            </thead>
            {renderTableBody()}
        </Table>
    );

    return (
        <div className="card">
            <div className="card-body">
                {isLoading ? <Spinner /> : renderTableUI()}
                <ModalConfirm
                    show={showModalDelete}
                    text={'Bạn có thực sự muốn xoá tin tức này?'}
                    btnDisabled={isDeleting}
                    changeShow={(s: boolean) => setShowModalDelete(s)}
                    submitAction={deleteItem}
                />

                <ModalContent
                    modalSize="xl"
                    title={`Chấm nhuận bút cho ${articleSelected?.title}`}
                    show={isOpenRoyaltyModal}
                    changeShow={onCloseRoyaltyModal}
                    content={<ArticleRoyaltyContent articleId={articleId} onCloseRoyaltyModal={onCloseRoyaltyModal} />}
                />
            </div>
        </div>
    );
}
