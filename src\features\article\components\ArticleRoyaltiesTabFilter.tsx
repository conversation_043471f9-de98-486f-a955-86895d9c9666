import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Nav, NavItem, NavLink } from 'reactstrap';
import { ArticleTypeNames } from '../../../types/common/Item';
import { NewArticleType } from '../../../types/Article';

interface IProps {
    activeTab: string;
    onTabChange: (tabId: string) => void;
}

export default function ArticleRoyaltiesTabFilter({ activeTab, onTabChange }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [tabs, setTabs] = useState<Array<{ id: string; name: string }>>([]);

    useEffect(() => {
        // Bỏ tab 'Chưa phân loại' (NewArticleType.UNCLASSIFIED)
        const allTabs = [
            { id: NewArticleType.ALL, name: 'Tất cả' },
            ...ArticleTypeNames.map((type) => ({ id: type.id.toString(), name: t(`${type.name}.single`) })),
        ];
        setTabs(allTabs);
    }, [t]);

    return (
        <div className="card">
            <div className="card-body">
                <Nav tabs className="mb-2">
                    {tabs.map((tab) => (
                        <NavItem key={tab.id}>
                            <NavLink
                                className={activeTab === tab.id ? 'active' : ''}
                                onClick={() => onTabChange(tab.id)}
                            >
                                {tab.name}
                            </NavLink>
                        </NavItem>
                    ))}
                </Nav>
                {/* Bỏ WorkflowTypeNames checkboxes */}
            </div>
        </div>
    );
}
