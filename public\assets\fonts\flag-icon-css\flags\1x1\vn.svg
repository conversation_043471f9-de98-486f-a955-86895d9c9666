<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg378" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3728">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs380">
  <clipPath id="clipPath4278" clipPathUnits="userSpaceOnUse">
   <rect id="rect4280" fill-opacity="0.67" height="708.66" width="708.66" y="-.0000033379" x="177.17"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath4278)" transform="matrix(.72249 0 0 .72249 -128 .0000024116)">
  <rect id="rect149" height="708.66" width="1063" y="0" x="0" stroke-width="1pt" fill="#ec0015"/>
  <path id="path205" d="m266.19 534.45-10.408-7.432-10.357 7.505 3.852-12.196-10.338-7.531 12.79-0.105 3.967-12.159 4.052 12.131 12.79 0.016-10.285 7.602 3.937 12.169z" transform="matrix(11.912 0 0 12.458 -2509.8 -6130.7)" stroke-width=".11287" fill="#ff0"/>
 </g>
</svg>
