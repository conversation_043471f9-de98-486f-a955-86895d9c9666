import { Nav, NavItem, NavLink } from 'reactstrap';
import { PressPublication } from 'types/PressPublication';

interface IProps {
    activeTab: string;
    onTabChange: (tabId: string) => void;
    pressPublications: PressPublication[];
}

export default function PressPublicationsTabFilter({ activeTab, onTabChange, pressPublications }: Readonly<IProps>) {
    return (
        <div className="card">
            <Nav tabs className="px-2 py-1 mb-0">
                {pressPublications.map((item) => (
                    <NavItem key={item.id}>
                        <NavLink
                            className={activeTab === item.id!.toString() ? 'active' : ''}
                            onClick={() => onTabChange(item.id!.toString())}
                        >
                            {item.name}
                        </NavLink>
                    </NavItem>
                ))}
            </Nav>
        </div>
    );
}
