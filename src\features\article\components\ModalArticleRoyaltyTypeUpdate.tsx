import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { selectItem, showToast, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { ArticleType, ArticleTypeNames, ItemStatus } from '../../../types/common/Item';
import ArticleRoyaltyType from 'types/ArticleRoyaltyType';
import InputSwitch from 'components/partials/InputSwitch';
import FormatNumber from 'components/partials/FormatNumber';

interface IProps {
    show: boolean;
    articleRoyaltyType?: ArticleRoyaltyType;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: ArticleRoyaltyType) => void;
}

export default function ModalArticleRoyaltyTypeUpdate({
    show,
    articleRoyaltyType,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const [fromRoyalty, setFromRoyalty] = useState(0);
    const [toRoyalty, setToRoyalty] = useState(0);

    const schema = yup.object({
        name: yup.string().required(t('error.required')).trim(),
        from_royalty: yup.number().typeError(t('error.number')).required(t('error.required')).min(0, t('error.min_0')),
        to_royalty: yup.number().typeError(t('error.number')).required(t('error.required')).min(0, t('error.min_0')),
    });

    const {
        register,
        handleSubmit,
        reset,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm<ArticleRoyaltyType>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (articleRoyaltyType && show) {
            reset({
                ...articleRoyaltyType,
                status_id: articleRoyaltyType.status_id === ItemStatus.ACTIVE,
            });
            setFromRoyalty(articleRoyaltyType.from_royalty);
            setToRoyalty(articleRoyaltyType.to_royalty);
        } else {
            reset({
                name: '',
                article_type_id: ArticleType.ELECTRONIC,
                from_royalty: 0,
                to_royalty: 0,
                is_default: false,
                status_id: true,
            });
            setFromRoyalty(0);
            setToRoyalty(0);
        }
    }, [articleRoyaltyType, show, reset]);

    const onSubmit = (data: ArticleRoyaltyType) => {
        if (fromRoyalty > toRoyalty) {
            showToast(false, ['Khoảng định mức không hợp lệ']);
            return;
        }
        submitAction(data);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-lg modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {articleRoyaltyType ? 'Cập nhật Phân loại tin bài' : 'Thêm mới Phân loại tin bài'}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form onSubmit={handleSubmit(onSubmit)} className="validate-form pt-50">
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6">
                                        <div className="mb-1">
                                            <label className="form-label">
                                                Tên <span className="error">*</span>
                                            </label>
                                            <input
                                                {...register('name')}
                                                className={classNames('form-control', {
                                                    'is-invalid': Boolean(errors.name?.message),
                                                })}
                                            />
                                            <span className="error">{errors.name?.message}</span>
                                        </div>
                                    </div>
                                    <div className="col-12 col-sm-6">
                                        <div className="mb-1">
                                            <label className="form-label">Loại</label>
                                            <select
                                                {...register('article_type_id', {
                                                    valueAsNumber: true,
                                                })}
                                                className="form-select"
                                            >
                                                {selectItem(ArticleTypeNames, t, true)}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-12 col-sm-6">
                                        <div className="mb-1">
                                            <label className="form-label">
                                                Khoảng định mức (Từ) <span className="error">*</span>
                                            </label>
                                            <FormatNumber
                                                value={fromRoyalty}
                                                isInput={true}
                                                onValueChange={(value: number) => {
                                                    setFromRoyalty(value);
                                                    setValue('from_royalty', value);
                                                    if (value >= 0) clearErrors('from_royalty');
                                                }}
                                            />
                                            <span className="error">{errors.from_royalty?.message}</span>
                                        </div>
                                    </div>
                                    <div className="col-12 col-sm-6">
                                        <div className="mb-1">
                                            <label className="form-label">
                                                Khoảng định mức (Đến) <span className="error">*</span>
                                            </label>
                                            <FormatNumber
                                                value={toRoyalty}
                                                isInput={true}
                                                onValueChange={(value: number) => {
                                                    setToRoyalty(value);
                                                    setValue('to_royalty', value);
                                                    if (value >= 0) clearErrors('to_royalty');
                                                }}
                                            />
                                            <span className="error">{errors.to_royalty?.message}</span>
                                        </div>
                                    </div>
                                    <InputSwitch
                                        classNameWrap="col-12 col-sm-6 mb-1"
                                        className="d-flex flex-column"
                                        labelSwitchName="Mặc định"
                                        labelFieldName="Mặc định"
                                        name="is_default"
                                        register={register}
                                    />
                                    <InputSwitch
                                        classNameWrap="col-12 col-sm-6 mb-1"
                                        className="d-flex flex-column"
                                        labelSwitchName="Hoạt động"
                                        labelFieldName="Trạng thái"
                                        name="status_id"
                                        register={register}
                                    />
                                    <div className="col-12 col-sm-12">
                                        <div className="mb-1">
                                            <label className="form-label">
                                                Ghi chú <span className="error">*</span>
                                            </label>
                                            <input {...register('desc')} className="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={articleRoyaltyType ? 'Cập nhật' : 'Thêm mới'}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
