import { baseFilterConfig, BaseModel, <PERSON>Search, DataList, FilterConfig } from './common';
import { ItemParam, ItemStatus } from './common/Item';
import { FILTER_CONDITIONS } from '../constants/common';

export default interface Workflow extends BaseModel {
    name: string;
    status_id: ItemStatus | boolean;
    desc?: string;
    display_order: number;
    workflow_type_id: number;
    department_id: number;
}

export interface WorkflowQuery {
    workflows_list: DataList<Workflow>;
}

export interface SearchWorkflow extends BaseSearch {
    workflow_type_id?: string;
    ids?: string;
}

export type SearchWorkflowParam = {
    [key in keyof SearchWorkflow]: string;
};

export const workflowFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    workflow_type_id: { key: 'workflow_type_id', operator: FILTER_CONDITIONS.IN },
    ids: { key: 'id', operator: FILTER_CONDITIONS.IN },
};

export enum WorkflowType {
    PRIVATE = 1,
    EDITING = 2,
    TIMING_PUBLISHING = 4,
    PUBLISHED = 3,
}

export const WorkflowTypeNames: ItemParam[] = [
    { id: WorkflowType.PRIVATE, name: 'workflowType1', className: 'badge badge-glow bg-danger' },
    { id: WorkflowType.EDITING, name: 'workflowType2', className: 'badge badge-glow bg-info' },
    { id: WorkflowType.TIMING_PUBLISHING, name: 'workflowType4', className: 'badge badge-glow bg-warning' },
    { id: WorkflowType.PUBLISHED, name: 'workflowType3', className: 'badge badge-glow bg-success' },
];
