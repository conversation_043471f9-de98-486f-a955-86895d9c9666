import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { find } from 'lodash';
import ContentHeader from '../../../components/partials/ContentHeader';
import Spinner from '../../../components/partials/Spinner';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import SearchForm from '../../../components/partials/SearchForm';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { convertConstantToSelectOptions, convertPaging, generateFilters, showToast } from '../../../utils/common';
import { PAGINATION, QUERY_KEY } from '../../../constants/common';
import { keepPreviousData } from '@tanstack/react-query';
import PaginationTable from '../../../components/partials/PaginationTable';
import {
    ARTICLE_ROYALTY_TYPE_CREATE,
    ARTICLE_ROYALTY_TYPE_DELETE,
    ARTICLE_ROYALTY_TYPE_LIST,
    ARTICLE_ROYALTY_TYPE_UPDATE,
} from 'services/ArticleRoyaltyTypeService';
import ArticleRoyaltyType, {
    ArticleRoyaltyTypeFilterConfig,
    ArticleRoyaltyTypeListQuery,
    SearchArticleRoyaltyType,
    SearchArticleRoyaltyTypeParam,
} from 'types/ArticleRoyaltyType';
import ListArticleRoyaltyType from '../components/ListArticleRoyaltyType';
import ModalArticleRoyaltyTypeUpdate from '../components/ModalArticleRoyaltyTypeUpdate';
import { ArticleTypeNames, ItemStatus, ItemStatusNames } from '../../../types/common/Item';

export default function ArticleRoyaltyTypeList() {
    const { t } = useTranslation();
    const [showUpdate, setShowUpdate] = useState<boolean>(false);
    const [showDelete, setShowDelete] = useState<boolean>(false);
    const [itemId, setItemId] = useState<number>(0);

    const { queryParams, setQueryParams } = useQueryParams<SearchArticleRoyaltyTypeParam>();
    const paramConfig: SearchArticleRoyaltyTypeParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            article_type_id: queryParams.article_type_id,
            status_id: queryParams.status_id,
        },
        isUndefined
    ) as SearchArticleRoyaltyTypeParam;

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, ArticleRoyaltyTypeFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<
        ArticleRoyaltyTypeListQuery,
        SearchArticleRoyaltyType
    >(
        [QUERY_KEY.ARTICLE_ROYALTY_TYPES, paramConfig, filters],
        ARTICLE_ROYALTY_TYPE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const articleRoyaltyTypes = data?.article_royalty_types_list.data ?? [];

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<ArticleRoyaltyType> }>(
        itemId > 0 ? ARTICLE_ROYALTY_TYPE_UPDATE : ARTICLE_ROYALTY_TYPE_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(ARTICLE_ROYALTY_TYPE_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: ArticleRoyaltyType) => {
        body.status_id = body.status_id ? ItemStatus.ACTIVE : ItemStatus.PENDING;
        delete body.id;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>Quản lý Phân loại tin bài</title>
            </Helmet>
            <ContentHeader
                title="Quản lý Phân loại tin bài"
                contextMenu={[
                    {
                        text: 'Thêm Phân loại tin bài',
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'article_type_id',
                                type: 'select',
                                label: 'Loại',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(ArticleTypeNames, t, true),
                                },
                            },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(ItemStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListArticleRoyaltyType
                                    items={articleRoyaltyTypes}
                                    paging={convertPaging<ArticleRoyaltyType, SearchArticleRoyaltyTypeParam>(
                                        data?.article_royalty_types_list,
                                        Number(paramConfig.limit)
                                    )}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                />
                                <PaginationTable
                                    countItem={data?.article_royalty_types_list.totalCount}
                                    totalPage={data?.article_royalty_types_list.totalPages}
                                    currentPage={data?.article_royalty_types_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalArticleRoyaltyTypeUpdate
                                show={showUpdate}
                                articleRoyaltyType={find(articleRoyaltyTypes, { id: itemId })}
                                isLoading={saveMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
