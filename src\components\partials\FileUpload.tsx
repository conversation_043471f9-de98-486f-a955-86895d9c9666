import classNames from 'classnames';
import { ChangeEvent, DragEvent, useRef, useState } from 'react';
import { AlertCircle, MoreVertical, UploadCloud } from 'react-feather';

const ALLOWED_TYPES = {
    image: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'],
    document: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/x-rar-compressed',
        'application/zip',
    ],
    video: ['video/mp4'],
    audio: ['audio/mpeg'],
};

interface Action {
    title: string;
    onClick: () => void;
    icon?: React.ReactNode;
}

interface IProps {
    type: 'image' | 'document' | 'video' | 'audio';
    onFileChange: (file: File) => void;
    actions?: Action[];
    mediaDefault?: string;
}

export default function FileUpload({ type, onFileChange, actions, mediaDefault }: Readonly<IProps>) {
    const inputRef = useRef<HTMLInputElement>(null);
    const [error, setError] = useState<string | null>(null);
    const [preview, setPreview] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [dragActive, setDragActive] = useState(false);
    const [showActions, setShowActions] = useState(false);

    const validateFile = (file: File): boolean => {
        setError(null);

        if (file.size > 10 * 1024 * 1024) {
            setError('Dung lượng file phải nhỏ hơn 10MB');
            return false;
        }

        if (!ALLOWED_TYPES[type].includes(file.type)) {
            setError('File không hợp lệ. Chỉ cho phép upload file với định dạng: ' + ALLOWED_TYPES[type].join(', '));
            return false;
        }

        return true;
    };

    const onChooseFile = () => inputRef.current?.click();

    const handleFile = (file: File) => {
        if (validateFile(file)) {
            setSelectedFile(file);
            onFileChange(file);
            const reader = new FileReader();

            switch (type) {
                case 'image':
                    // reader.onload = (e) => setPreview(e.target?.result as string);
                    reader.readAsDataURL(file);
                    break;
                case 'video':
                case 'audio':
                    setPreview(URL.createObjectURL(file));
                    break;
                default:
                    setPreview(null);
            }
        } else {
            setPreview(null);
            setSelectedFile(null);
            //onFileChange(null);
        }
    };

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(true);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleDrag = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else {
            setDragActive(false);
        }
    };

    return (
        <div className="w-full mx-auto">
            <div
                className={classNames(`relative border-2 border-dashed rounded-lg p-6`, {
                    'border-blue-500 bg-blue-50': dragActive,
                    'border-gray-300': !dragActive,
                })}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
            >
                <input
                    type="file"
                    className="hidden"
                    ref={inputRef}
                    accept={ALLOWED_TYPES[type].join(',')}
                    onChange={handleChange}
                />
                <div className="text-center">
                    <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                        {'Kéo thả file vào đây hoặc '}
                        <button type="button" className="text-primary" onClick={onChooseFile}>
                            nhấn chọn file
                        </button>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                        {type === 'image' && 'Hỗ trợ các định dạng: PNG, JPG, JPEG, GIF'}
                        {type === 'document' && 'Hỗ trợ các định dạng: DOC, DOCX'}
                        {type === 'video' && 'Hỗ trợ định dạng: MP4'}
                        {type === 'audio' && 'Hỗ trợ định dạng: MP3'}
                    </p>
                </div>

                {error && (
                    <div className="mt-4 text-red-500 d-flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        <span>{error}</span>
                    </div>
                )}

                {selectedFile && type === 'document' && (
                    <div className="mt-4">
                        <p className="text-sm font-medium">File đã chọn:</p>
                        <p className="text-sm text-gray-500">{selectedFile.name}</p>
                    </div>
                )}

                {(preview || mediaDefault) && (
                    <div className="relative mt-4">
                        {type === 'image' && (
                            <>
                                <img
                                    src={preview || mediaDefault}
                                    alt="Preview"
                                    className="w-full max-h-[300px] object-cover rounded"
                                />
                                {actions && actions.length > 0 && (
                                    <div className="absolute top-2 right-2">
                                        <button
                                            type="button"
                                            onClick={() => setShowActions(!showActions)}
                                            className="bg-white rounded-full p-2 shadow-lg"
                                        >
                                            <MoreVertical className="h-5 w-5" />
                                        </button>
                                        {showActions && (
                                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1">
                                                {actions.map((action, index) => (
                                                    <button
                                                        key={index}
                                                        onClick={() => {
                                                            action.onClick();
                                                            setShowActions(false);
                                                        }}
                                                        className="d-flex items-center px-4 py-2 w-full text-left hover:bg-gray-100"
                                                    >
                                                        {action.icon && <span className="mr-2">{action.icon}</span>}
                                                        {action.title}
                                                    </button>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </>
                        )}
                        {type === 'video' && (
                            <video controls className="max-w-full h-auto rounded">
                                <source src={preview || mediaDefault} type="video/mp4" />
                            </video>
                        )}
                        {type === 'audio' && (
                            <audio controls className="w-full">
                                <source src={preview || mediaDefault} type="audio/mpeg" />
                            </audio>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
